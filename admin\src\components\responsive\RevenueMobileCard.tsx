import React from "react";
import { useResponsive } from "@/hooks/useResponsive";

interface RevenueTableRow {
  key: string;
  metric: string;
  total: number;
  [period: string]: string | number;
}

interface RevenueMobileCardProps {
  record: RevenueTableRow;
  renderRevenue: (revenue: number, isTotal?: boolean) => React.ReactElement;
}

export const RevenueMobileCard: React.FC<RevenueMobileCardProps> = ({
  record,
  renderRevenue,
}) => {
  const { isMobile } = useResponsive();
  const isRevenueRow = record.metric === "Doanh thu";
  const isOrdersRow = record.metric === "Tổng đơn hàng";
  
  // Get all period columns (exclude metric, key, total)
  const periodColumns = Object.entries(record).filter(
    ([key, value]) => key !== "metric" && key !== "key" && key !== "total" && typeof value === "number"
  );

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-4 transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5">
      <div className="mb-3 border-b border-gray-100 pb-3">
        <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold ${
          isRevenueRow ? "text-blue-600" : 
          isOrdersRow ? "text-green-600" : 
          "text-purple-600"
        }`}>
          {record.metric}
        </h3>
      </div>
      
      <div className="space-y-3">
        {/* Period columns */}
        <div className="grid grid-cols-1 gap-2">
          {periodColumns.map(([period, value]) => (
            <div key={period} className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-sm font-medium text-gray-600">{period}</span>
              <span className="text-sm font-semibold">
                {isRevenueRow 
                  ? renderRevenue(value as number)
                  : new Intl.NumberFormat("vi-VN").format(value as number)
                }
              </span>
            </div>
          ))}
        </div>
        
        {/* Total */}
        <div className="flex justify-between items-center py-2 border-t-2 border-gray-300 bg-blue-50 rounded px-2">
          <span className="text-base font-semibold text-blue-800">Tổng</span>
          <span className="text-base font-bold text-blue-800">
            {isRevenueRow 
              ? renderRevenue(record.total, true)
              : new Intl.NumberFormat("vi-VN").format(record.total)
            }
          </span>
        </div>
      </div>
    </div>
  );
};
