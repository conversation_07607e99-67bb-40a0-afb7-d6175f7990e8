import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

interface ExportColumn<T> {
  header: string;
  key: keyof T;
  width?: number;
  render?: (value: any, record: T) => string | number;
}

interface ExportSheet<T> {
  name: string;
  data: T[];
  columns: ExportColumn<T>[];
}

const styleWorksheetHeader = (worksheet: ExcelJS.Worksheet) => {
  worksheet.getRow(1).eachCell(cell => {
    cell.font = { bold: true };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD3D3D3' },
    };
    cell.alignment = { vertical: 'middle', horizontal: 'center' };
  });
};

const addDataToWorksheet = <T extends Record<string, any>>(
  worksheet: ExcelJS.Worksheet,
  data: T[],
  columns: ExportColumn<T>[]
) => {
  data.forEach(item => {
    const row: { [key: string]: any } = {};
    columns.forEach(col => {
      const value = item[col.key];
      row[col.key as string] = col.render ? col.render(value, item) : value;
    });
    worksheet.addRow(row);
  });
};

export const exportToExcel = async <T extends Record<string, any>>(
  data: T[],
  columns: ExportColumn<T>[],
  fileName: string
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Sheet 1');

  // Add headers
  worksheet.columns = columns.map(col => ({
    header: col.header,
    key: col.key as string,
    width: col.width || 20,
  }));

  // Style header
  styleWorksheetHeader(worksheet);

  // Add data rows
  addDataToWorksheet(worksheet, data, columns);

  // Save the file
  const buffer = await workbook.xlsx.writeBuffer();
  saveAs(new Blob([buffer]), `${fileName}.xlsx`);
};

export const exportToExcelMultiSheet = async <T extends Record<string, any>>(
  sheets: ExportSheet<T>[],
  fileName: string
) => {
  if (sheets.length === 0) {
    throw new Error('At least one sheet is required for export');
  }

  const workbook = new ExcelJS.Workbook();

  sheets.forEach(({ name, data, columns }) => {
    const worksheet = workbook.addWorksheet(name);

    // Add headers
    worksheet.columns = columns.map(col => ({
      header: col.header,
      key: col.key as string,
      width: col.width || 20,
    }));

    // Style header
    styleWorksheetHeader(worksheet);

    // Add data rows
    addDataToWorksheet(worksheet, data, columns);
  });

  // Save the file
  const buffer = await workbook.xlsx.writeBuffer();
  saveAs(new Blob([buffer]), `${fileName}.xlsx`);
};
