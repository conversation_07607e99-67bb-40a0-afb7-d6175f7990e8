import { useState } from "react";
import { Input, Select, Button } from "antd";

interface ProductRevenueSearchProps {
  onSearch: (params: ProductRevenueSearchParams) => void;
  initialParams?: ProductRevenueSearchParams;
}

export interface ProductRevenueSearchParams {
  query?: string;
  searchBy: "code" | "name";
}

export function ProductRevenueSearch({ onSearch, initialParams }: ProductRevenueSearchProps) {
  const [searchParams, setSearchParams] = useState<ProductRevenueSearchParams>(() => ({
    searchBy: initialParams?.searchBy || "code",
    query: initialParams?.query,
  }));

  const handleSearch = () => {
    onSearch(searchParams);
  };

  const handleReset = () => {
    const resetParams = { searchBy: "code" as const };
    setSearchParams(resetParams);
    onSearch(resetParams);
  };

  const handleQueryChange = (value: string) => {
    setSearchParams({
      ...searchParams,
      query: value,
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-[200px]">
          <div className="flex gap-2">
            <Input
              placeholder="Tìm kiếm sản phẩm..."
              value={searchParams.query || ""}
              onChange={(e) => handleQueryChange(e.target.value)}
              className="flex-1"
              size="large"
              onPressEnter={() => handleSearch()}
            />
            <Select
              value={searchParams.searchBy}
              onChange={(value) =>
                setSearchParams({
                  ...searchParams,
                  searchBy: value as ProductRevenueSearchParams["searchBy"],
                })
              }
              className="w-[140px]"
              size="large"
              options={[
                { value: "code", label: "Mã hàng" },
                { value: "name", label: "Tên sản phẩm" },
              ]}
            />
          </div>
        </div>

        <Button type="primary" size="large" onClick={handleSearch}>
          Tìm kiếm
        </Button>
        <Button size="large" onClick={handleReset}>
          Đặt lại
        </Button>
      </div>
    </div>
  );
}
