```
feat: implement arito scraping API with pagination and inventory update

- Add simple scraping endpoint /api/scrape_inventory/
- Use threading to avoid 30s timeout as requested by tech lead
- Store credentials in .env file instead of database models
- Use JSON config file for scraping configuration
- **Support automatic pagination scraping across all pages**
- **Auto-detect total pages and navigate through pagination**
- **Configurable max_pages limit and page_delay for safety**
- **Automatic inventory update after scraping**
- **Product matching by code and stock update in database**
- **Comprehensive error handling and validation**
- Integrate healthchecks.io monitoring
- Integrate Discord bot for error notifications
- Remove complex Celery/database dependencies
- Add test scripts for validation
- Successfully tested: 149 products scraped, 2 products updated in database

Features:
- ✅ Automatic pagination detection and navigation
- ✅ Automatic inventory update after scraping
- ✅ Product matching by code with case-insensitive search
- ✅ Stock validation and conversion (handles negative values)
- ✅ Safety limits (max 100 pages by default)
- ✅ Configurable delays between pages (2s default)
- ✅ Robust error handling per page and per product
- ✅ Threading for timeout avoidance
- ✅ Health monitoring and Discord notifications
- ✅ Comprehensive logging and result reporting

Files added:
- store/views/scraping_views.py
- store/services/scraping_service.py (with inventory update function)
- scraping_config.json
- test_inventory_update.py (test inventory update)
- test_scraping_with_update.py (test full flow)
- README_SCRAPING.md

Files modified:
- store/urls.py (add new endpoint)
- .env (add scraping credentials)
- requirements.txt (add playwright)

Ready for crontap.com scheduling every 1-2 hours
Can scrape thousands of products across multiple pages and update inventory automatically
Complete end-to-end solution: scraping → data extraction → inventory update → monitoring
```
