import React from "react";
import { Card, Form, Input, Select } from "antd";
import { useLocationSelect } from "@/hooks/useLocationSelect";

interface ChainOrderForm {
  user?: number;
  phone_number: string;
  email: string;
  shipping_address: string;
  ward: string;
  district: string;
  city: string;
  payment_method: "cod" | "cash" | "bank_transfer";
  payment_status: "paid" | "unpaid";
  company_payment_received: boolean;
  shipping_fee?: number;
  discount?: number;
  is_chain: boolean;
  tax?: number;
  have_tax: boolean;
  sales_admin?: number;
}

interface ShippingAddressFormProps {
  orderForm: ChainOrderForm;
  onOrderFormChange: (field: string, value: string) => void;
}

const ShippingAddressForm: React.FC<ShippingAddressFormProps> = ({
  orderForm,
  onOrderFormChange,
}) => {
  const {
    cities,
    districts,
    wards,
    selectedCity,
    selectedDistrict,
    selectedWard,
    loadingCities,
    loadingDistricts,
    loadingWards,
    handleCityChange,
    handleDistrictChange,
    handleWardChange,
  } = useLocationSelect({
    initialCity: orderForm.city,
    initialDistrict: orderForm.district,
    initialWard: orderForm.ward,
    onCityChange: (_, label) => onOrderFormChange("city", label),
    onDistrictChange: (_, label) => onOrderFormChange("district", label),
    onWardChange: (_, label) => onOrderFormChange("ward", label),
  });

  return (
    <Card title="Thông tin giao hàng" className="mt-6">
      <Form layout="vertical">
        <Form.Item label="Địa chỉ giao hàng" required>
          <Input
            placeholder="Nhập địa chỉ chi tiết (số nhà, tên đường...)"
            value={orderForm.shipping_address}
            onChange={(e) => onOrderFormChange("shipping_address", e.target.value)}
            className="w-full"
          />
        </Form.Item>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Form.Item label="Thành phố" required>
            <Select
              placeholder="Chọn thành phố"
              value={selectedCity || undefined}
              onChange={handleCityChange}
              loading={loadingCities}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
              }
              options={cities}
              className="w-full"
            />
          </Form.Item>

          <Form.Item label="Quận/Huyện" required>
            <Select
              placeholder="Chọn quận/huyện"
              value={selectedDistrict || undefined}
              onChange={handleDistrictChange}
              loading={loadingDistricts}
              disabled={!selectedCity}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
              }
              options={districts}
              className="w-full"
            />
          </Form.Item>

          <Form.Item label="Phường/Xã" required>
            <Select
              placeholder="Chọn phường/xã"
              value={selectedWard || undefined}
              onChange={handleWardChange}
              loading={loadingWards}
              disabled={!selectedDistrict}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
              }
              options={wards}
              className="w-full"
            />
          </Form.Item>
        </div>
      </Form>
    </Card>
  );
};

export default ShippingAddressForm;
export type { ShippingAddressFormProps, ChainOrderForm };
