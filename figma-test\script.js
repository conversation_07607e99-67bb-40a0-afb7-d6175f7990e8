// ParkMate Admin Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the dashboard
    initializeDashboard();
});

function initializeDashboard() {
    // Add event listeners
    setupNavigationEvents();
    setupCardEvents();
    setupParkingSlotEvents();
    setupNotificationEvents();
    
    // Initialize parking slot states
    initializeParkingSlots();
    
    // Update statistics
    updateStatistics();
}

// Navigation Events
function setupNavigationEvents() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            navItems.forEach(nav => nav.classList.remove('active'));
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Update page title based on selected nav item
            const navText = this.querySelector('span').textContent;
            updatePageTitle(navText);
        });
    });
}

// Card Events
function setupCardEvents() {
    // Close button events
    const closeButtons = document.querySelectorAll('.close-btn');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const card = this.closest('.stat-card');
            card.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                card.style.display = 'none';
            }, 300);
        });
    });
    
    // Detail button events
    const detailButtons = document.querySelectorAll('.detail-btn');
    detailButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const cardTitle = this.closest('.stat-card').querySelector('.card-title').textContent;
            showDetailModal(cardTitle);
        });
    });
}

// Parking Slot Events
function setupParkingSlotEvents() {
    const parkingSlots = document.querySelectorAll('.parking-slot');
    
    parkingSlots.forEach(slot => {
        slot.addEventListener('click', function() {
            toggleParkingSlot(this);
        });
        
        // Add hover effect for better UX
        slot.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0px 4px 8px rgba(0, 0, 0, 0.2)';
        });
        
        slot.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}

// Notification Events
function setupNotificationEvents() {
    const bellIcon = document.querySelector('.navbar-actions .icon-btn:first-child');
    const userIcon = document.querySelector('.navbar-actions .icon-btn:last-child');
    
    bellIcon.addEventListener('click', function() {
        showNotifications();
    });
    
    userIcon.addEventListener('click', function() {
        showUserMenu();
    });
}

// Initialize parking slots with random states
function initializeParkingSlots() {
    const slots = document.querySelectorAll('.parking-slot');
    const occupiedSlots = [1, 3, 5]; // Predefined occupied slots to match design
    
    slots.forEach((slot, index) => {
        const slotNumber = index + 1;
        if (occupiedSlots.includes(slotNumber)) {
            slot.classList.remove('available');
            slot.classList.add('occupied');
        } else {
            slot.classList.remove('occupied');
            slot.classList.add('available');
        }
    });
    
    updateParkingStatus();
}

// Toggle parking slot status
function toggleParkingSlot(slot) {
    if (slot.classList.contains('occupied')) {
        slot.classList.remove('occupied');
        slot.classList.add('available');
        showToast('Parking slot freed!', 'success');
    } else {
        slot.classList.remove('available');
        slot.classList.add('occupied');
        showToast('Parking slot occupied!', 'info');
    }
    
    updateParkingStatus();
    updateStatistics();
}

// Update parking status display
function updateParkingStatus() {
    const availableSlots = document.querySelectorAll('.parking-slot.available').length;
    const totalSlots = document.querySelectorAll('.parking-slot').length;
    const occupiedSlots = totalSlots - availableSlots;
    
    const statusText = document.querySelector('.parking-title');
    statusText.textContent = `Parking status: ${availableSlots} Available, ${occupiedSlots} Occupied`;
}

// Update statistics
function updateStatistics() {
    const occupiedSlots = document.querySelectorAll('.parking-slot.occupied').length;
    const vehicleCountElement = document.querySelector('.stat-card:first-child .card-value');
    
    if (vehicleCountElement) {
        vehicleCountElement.textContent = occupiedSlots.toString();
    }
    
    // Simulate revenue calculation (1000 VND per occupied slot)
    const revenue = occupiedSlots * 1000;
    const revenueElement = document.querySelector('.stat-card:last-child .card-value');
    
    if (revenueElement) {
        revenueElement.textContent = `${revenue.toLocaleString()} VND`;
    }
}

// Update page title
function updatePageTitle(title) {
    const pageTitle = document.querySelector('.page-title');
    pageTitle.textContent = title;
}

// Show detail modal
function showDetailModal(cardTitle) {
    const modal = createModal(cardTitle);
    document.body.appendChild(modal);
    
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

// Create modal element
function createModal(title) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title} Details</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>Detailed information for ${title} would be displayed here.</p>
                <p>This is a demo implementation.</p>
            </div>
        </div>
    `;
    
    // Add modal styles
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    const modalContent = modal.querySelector('.modal-content');
    modalContent.style.cssText = `
        background: white;
        padding: 24px;
        border-radius: 8px;
        max-width: 500px;
        width: 90%;
        max-height: 80%;
        overflow-y: auto;
    `;
    
    // Close modal events
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal(modal);
        }
    });
    
    modal.querySelector('.modal-close').addEventListener('click', function() {
        closeModal(modal);
    });
    
    return modal;
}

// Close modal
function closeModal(modal) {
    modal.style.opacity = '0';
    setTimeout(() => {
        modal.remove();
    }, 300);
}

// Show notifications
function showNotifications() {
    showToast('No new notifications', 'info');
}

// Show user menu
function showUserMenu() {
    showToast('User menu clicked', 'info');
}

// Show toast notification
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 3000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    // Set background color based on type
    const colors = {
        success: '#2AD95B',
        info: '#0388B4',
        warning: '#FFA500',
        error: '#FF0B1F'
    };
    
    toast.style.backgroundColor = colors[type] || colors.info;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 10);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        from { opacity: 1; transform: scale(1); }
        to { opacity: 0; transform: scale(0.9); }
    }
    
    .modal-overlay.show {
        opacity: 1 !important;
    }
    
    .toast {
        box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
    }
`;
document.head.appendChild(style);
