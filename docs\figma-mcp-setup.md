# Figma MCP Server Setup Guide

This guide will help you set up the Figma Model Context Protocol (MCP) server to work with Augment Code, allowing AI agents to access your Figma designs directly.

## Overview

The Figma MCP server enables Augment Code to:
- Access Figma file data directly from URLs
- Understand design layouts, components, and styling
- Generate accurate code implementations from Figma designs
- Provide better context for design-to-code workflows

## Prerequisites

1. **Figma Account** with access to the files you want to use
2. **Node.js** installed on your system (for running the MCP server)
3. **Augment Code** extension installed in VS Code

## Step 1: Create Figma API Token

1. Go to [Figma Account Settings](https://www.figma.com/settings)
2. Navigate to "Personal access tokens" section
3. Click "Create new token"
4. Give it a descriptive name (e.g., "Augment MCP Server")
5. Copy the generated token (you won't be able to see it again)

## Step 2: Configure MCP Server

### Option A: Using Augment Settings Panel (Recommended)

1. Open VS Code with Augment Code extension
2. Open the Augment panel
3. Click the gear icon (⚙️) in the upper right
4. In the MCP section, click the "+" button
5. Fill in the configuration:
   - **Name**: `Framelink Figma MCP`
   - **Command**: `npx -y figma-developer-mcp --figma-api-key=YOUR_API_TOKEN --stdio`
   - Replace `YOUR_API_TOKEN` with your actual Figma API token

### Option B: Manual Configuration

The configuration has been pre-created in `.vscode/settings.json`. You need to:

1. Open `.vscode/settings.json`
2. Replace `YOUR_FIGMA_API_KEY_HERE` with your actual Figma API token
3. Save the file
4. Restart VS Code

## Step 3: Test the Configuration

1. Restart VS Code to load the new MCP server
2. Open Augment Agent (chat panel)
3. Try a test prompt like:
   ```
   Please analyze this Figma design: [paste your Figma file URL here]
   ```
4. The agent should be able to access and describe the Figma file

## Usage Examples

### Basic Design Analysis
```
Analyze this Figma design and tell me about its layout:
https://www.figma.com/file/[your-file-id]/[your-file-name]
```

### Code Generation
```
Implement this Figma design as a React component:
https://www.figma.com/file/[your-file-id]/[your-file-name]

Use Tailwind CSS for styling and make it responsive.
```

### Component Extraction
```
Extract the button component from this Figma frame and create a reusable React component:
https://www.figma.com/file/[your-file-id]/[your-file-name]?node-id=[node-id]
```

## Troubleshooting

### Common Issues

1. **"MCP server not found" error**
   - Ensure Node.js is installed
   - Check that the command path is correct
   - Restart VS Code after configuration

2. **"Invalid API token" error**
   - Verify your Figma API token is correct
   - Ensure the token has proper permissions
   - Check for any extra spaces in the token

3. **"Cannot access Figma file" error**
   - Ensure you have access to the Figma file
   - Check that the file URL is correct
   - Verify the file is not private (or you have access)

### Debug Mode

To enable debug logging, modify the command in settings:
```json
"args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR_API_TOKEN", "--stdio", "--debug"]
```

## Security Notes

- Keep your Figma API token secure and never commit it to version control
- Consider using environment variables for the API token in production
- The token provides access to all your Figma files, so treat it like a password

## Advanced Configuration

### Using Environment Variables

Instead of putting the API key directly in the command, you can use environment variables:

1. Set the environment variable:
   ```bash
   export FIGMA_API_KEY=your_actual_api_token
   ```

2. Update the configuration:
   ```json
   {
     "name": "Framelink Figma MCP",
     "command": "npx",
     "args": ["-y", "figma-developer-mcp", "--stdio"],
     "env": {
       "FIGMA_API_KEY": "your_actual_api_token"
     }
   }
   ```

## Next Steps

Once configured, you can:
- Use Figma URLs directly in Augment Agent conversations
- Ask for design analysis and implementation
- Generate components based on Figma designs
- Create responsive layouts from Figma mockups

## Resources

- [Figma MCP Server GitHub](https://github.com/GLips/Figma-Context-MCP)
- [Framelink Documentation](https://www.framelink.ai/docs)
- [Augment MCP Documentation](https://docs.augmentcode.com/setup-augment/mcp)
- [Model Context Protocol](https://modelcontextprotocol.io/)
