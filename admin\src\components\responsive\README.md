# Responsive Components for Admin Panel

A comprehensive responsive design system for the 3T Admin Panel that provides mobile-first, touch-friendly components while maintaining desktop functionality.

## Overview

This responsive system transforms the admin panel to work seamlessly across all devices:
- **Mobile**: 375px-428px (iPhone 11/12/13, Samsung Galaxy)
- **Tablet**: 429px-1024px (iPad, Android tablets)  
- **Desktop**: 1025px+ (existing functionality preserved)

## Key Features

### 🎯 Mobile-First Design
- Touch targets ≥ 44px for optimal usability
- Drawer navigation with gesture support
- Card-based table views for mobile
- Optimized form layouts per device

### ⚡ Performance Optimized
- CSS Custom Properties for consistent theming
- Efficient breakpoint detection with SSR safety
- Smooth animations with reduced motion support
- Minimal bundle size impact

### ♿ Accessibility Ready
- High contrast mode support
- Keyboard navigation (Escape to close menus)
- Screen reader friendly markup
- Print-optimized layouts

## Components

### ResponsiveAdminLayout

The main layout component that adapts based on screen size:

```tsx
import { ResponsiveAdminLayout } from '@/components/responsive';

function App() {
  return (
    <ResponsiveAdminLayout>
      {/* Your page content */}
    </ResponsiveAdminLayout>
  );
}
```

**Features:**
- **Mobile**: Drawer sidebar with overlay
- **Tablet**: Larger drawer with enhanced header
- **Desktop**: Fixed sidebar (original behavior)
- Auto-close on route changes
- Keyboard navigation support

### ResponsiveTable

Transforms Ant Design tables into mobile-friendly card layouts:

```tsx
import { ResponsiveTable, createMobileAction } from '@/components/responsive';

const columns = [
  { title: 'Name', dataIndex: 'name' },
  { title: 'Email', dataIndex: 'email' },
  // ... more columns
];

function MyTable() {
  return (
    <ResponsiveTable
      columns={columns}
      dataSource={data}
      mobileCardTitle={(record) => record.name}
      mobileCardActions={(record) => [
        createMobileAction('Edit', () => handleEdit(record.id)),
        createMobileAction('Delete', () => handleDelete(record.id), 'default', true)
      ]}
    />
  );
}
```

**Features:**
- **Mobile/Tablet**: Card-based view with all data visible
- **Desktop**: Standard Ant Design table
- Configurable mobile card layouts
- Helper functions for responsive actions
- TypeScript support with proper generics

### ResponsiveForm

Provides device-optimized form layouts:

```tsx
import { 
  ResponsiveForm, 
  ResponsiveFormRow, 
  ResponsiveFormActions,
  ResponsiveFormItem 
} from '@/components/responsive';

function MyForm() {
  return (
    <ResponsiveForm>
      <ResponsiveFormRow>
        <ResponsiveFormItem label="Name" name="name">
          <Input />
        </ResponsiveFormItem>
        <ResponsiveFormItem label="Email" name="email">
          <Input />
        </ResponsiveFormItem>
      </ResponsiveFormRow>
      
      <ResponsiveFormActions>
        <Button type="primary">Save</Button>
        <Button>Cancel</Button>
      </ResponsiveFormActions>
    </ResponsiveForm>
  );
}
```

**Features:**
- **Mobile**: Vertical layout, stacked buttons
- **Tablet**: Horizontal layout, inline buttons  
- **Desktop**: Flexible layout options
- Touch-friendly inputs (44px+ height)
- Optimized validation for mobile

## Hooks

### useResponsiveState

Comprehensive responsive state management:

```tsx
import { useResponsiveState } from '@/components/responsive';

function MyComponent() {
  const { isMobile, isTablet, isDesktop, width, height } = useResponsiveState();
  
  return (
    <div>
      {isMobile && <MobileView />}
      {isTablet && <TabletView />}
      {isDesktop && <DesktopView />}
    </div>
  );
}
```

### Individual Device Hooks

For specific device detection:

```tsx
import { useIsMobile, useIsTablet, useIsDesktop } from '@/components/responsive';

function MyComponent() {
  const isMobile = useIsMobile();
  
  if (isMobile) {
    return <MobileSpecificComponent />;
  }
  
  return <RegularComponent />;
}
```

## Helper Functions

### createMobileAction

Creates touch-friendly action buttons:

```tsx
import { createMobileAction } from '@/components/responsive';

const editAction = createMobileAction(
  'Edit',           // label
  () => handleEdit(), // onClick handler
  'primary',        // button type
  false,           // danger
  <EditOutlined /> // icon
);
```

### createResponsiveColumn

Creates responsive table columns:

```tsx
import { createResponsiveColumn } from '@/components/responsive';

const nameColumn = createResponsiveColumn(
  'Name',
  'name',
  (value, record) => <strong>{value}</strong>,
  200,  // width
  undefined, // fixed
  true  // sortable
);
```

## CSS Classes

The responsive system includes utility CSS classes:

```css
/* Touch-friendly elements */
.touch-target        /* 44px minimum touch target */
.touch-button        /* Touch-optimized button */

/* Layout containers */
.responsive-container    /* Mobile-first container */
.responsive-content     /* Content area with proper spacing */

/* Mobile navigation */
.mobile-header          /* Mobile header bar */
.mobile-sidebar         /* Drawer sidebar */
.mobile-sidebar-overlay /* Background overlay */

/* Table layouts */
.responsive-table-container      /* Table wrapper */
.responsive-table-mobile-cards   /* Mobile card container */
.responsive-table-mobile-card    /* Individual mobile card */

/* Form layouts */
.responsive-form        /* Form container */
.responsive-form-row    /* Form field row */
.responsive-form-actions /* Form button container */
```

## Breakpoints

```typescript
const breakpoints = {
  mobile: 428,    // Mobile devices
  tablet: 1024,   // Tablet devices  
  desktop: 1025,  // Desktop devices
  
  // Legacy Tailwind breakpoints (for compatibility)
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};
```

## Migration Guide

### From AdminLayout to ResponsiveAdminLayout

```tsx
// Before
import { AdminLayout } from '@/components';

// After  
import { ResponsiveAdminLayout } from '@/components/responsive';
```

### From Ant Design Table to ResponsiveTable

```tsx
// Before
import { Table } from 'antd';
<Table columns={columns} dataSource={data} />

// After
import { ResponsiveTable } from '@/components/responsive';
<ResponsiveTable 
  columns={columns} 
  dataSource={data}
  mobileCardTitle={(record) => record.name}
/>
```

### From Ant Design Form to ResponsiveForm

```tsx
// Before
import { Form } from 'antd';
<Form layout="horizontal">

// After
import { ResponsiveForm } from '@/components/responsive';
<ResponsiveForm>
```

## Best Practices

### 1. Mobile-First Approach
Always design for mobile first, then enhance for larger screens:

```tsx
function MyComponent() {
  const { isMobile, isTablet } = useResponsiveState();
  
  // Default to mobile design
  let columns = mobileColumns;
  
  // Enhance for larger screens
  if (isTablet) {
    columns = [...columns, ...additionalTabletColumns];
  }
  
  return <ResponsiveTable columns={columns} />;
}
```

### 2. Touch Targets
Ensure all interactive elements meet minimum touch target sizes:

```tsx
// Good - uses touch-friendly button
<Button className="touch-button">Save</Button>

// Better - uses helper function
{createMobileAction('Save', handleSave, 'primary')}
```

### 3. Content Prioritization
Show most important content first on mobile:

```tsx
const mobileCardTitle = (record) => {
  // Show most important info in title
  return `${record.customerName} - ${record.orderTotal}`;
};
```

### 4. Performance Considerations
Use device detection to avoid loading unnecessary components:

```tsx
function Dashboard() {
  const { isDesktop } = useResponsiveState();
  
  return (
    <div>
      <MobileStats />
      {isDesktop && <AdvancedCharts />} {/* Only load on desktop */}
    </div>
  );
}
```

## Testing

The responsive components have been tested on:
- ✅ iPhone 11/12/13 (375px-428px)
- ✅ Samsung Galaxy (375px-428px)  
- ✅ iPad (768px-1024px)
- ✅ Android Tablets (768px-1024px)
- ✅ Desktop (1025px+)

## Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## Contributing

When adding new responsive components:

1. Follow mobile-first approach
2. Ensure touch targets ≥ 44px
3. Test across all target devices
4. Include TypeScript types
5. Add proper documentation
6. Follow existing naming conventions

## Troubleshooting

### Common Issues

**1. Table not showing mobile cards**
- Ensure you're using `ResponsiveTable` instead of `Table`
- Check that `mobileCardTitle` is provided

**2. Form inputs too small on mobile**
- Use `ResponsiveForm` wrapper
- Ensure inputs have `responsive-form` class applied

**3. Navigation drawer not working**
- Verify `ResponsiveAdminLayout` is being used
- Check for CSS conflicts with existing styles

**4. TypeScript errors with table columns**
- Use `createResponsiveColumn` helper function
- Ensure proper generic types are provided

For more help, check the implementation examples in the existing admin pages.
