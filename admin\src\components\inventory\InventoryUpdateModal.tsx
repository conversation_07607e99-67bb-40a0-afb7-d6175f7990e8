import React, { useState } from 'react';
import { Modal, Button, Table, Tag, Space, message, Spin, Alert, Statistic, Row, Col } from 'antd';
import { ReloadOutlined, ClockCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiCall, endpoints } from '@/lib/api';
import { InventoryUpdateLog, InventoryUpdateStats, InventoryLogListResponse } from '@/types/inventory';
import { useToast } from '@/context/toast-hooks';

interface InventoryUpdateModalProps {
  open: boolean;
  onClose: () => void;
}

export const InventoryUpdateModal: React.FC<InventoryUpdateModalProps> = ({ open, onClose }) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const { showToast } = useToast();
  const queryClient = useQueryClient();

  // Fetch inventory logs
  const { data: logsData, isLoading: logsLoading, refetch: refetchLogs } = useQuery<InventoryLogListResponse>({
    queryKey: ['inventory-logs'],
    queryFn: () => apiCall('GET', `${endpoints.inventory.logs}?page_size=10`),
    enabled: open,
  });

  // Fetch inventory stats
  const { data: statsData, isLoading: statsLoading } = useQuery<InventoryUpdateStats>({
    queryKey: ['inventory-stats'],
    queryFn: () => apiCall('GET', endpoints.inventory.stats),
    enabled: open,
  });

  // Trigger manual update mutation
  const triggerUpdateMutation = useMutation({
    mutationFn: () => apiCall('GET', endpoints.inventory.triggerUpdate),
    onSuccess: (data: any) => {
      // Check if response indicates success
      if (data?.status === 'ok, task started') {
        showToast('Yêu cầu cập nhật tồn kho đã được gửi đi. Vui lòng chờ cập nhật trong vài phút.', 'success');
        // Refetch logs after a short delay to show the new pending log
        setTimeout(() => {
          refetchLogs();
          queryClient.invalidateQueries({ queryKey: ['inventory-stats'] });
        }, 2000);
        // Auto close modal after successful trigger
        onClose();
      } else {
        showToast('Có lỗi xảy ra khi khởi động cập nhật tồn kho', 'error');
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Không thể khởi động cập nhật tồn kho';
      showToast(errorMessage, 'error');
    },
  });

  const handleTriggerUpdate = () => {
    Modal.confirm({
      title: 'Xác nhận cập nhật tồn kho',
      content: (
        <div>
          <p>Bạn có chắc chắn muốn cập nhật tồn kho từ Arito ngay bây giờ?</p>
          <p><strong>Lưu ý:</strong> Quá trình này có thể mất 10-15 phút. Vui lòng không nhấn lại trong khi đang xử lý.</p>
        </div>
      ),
      okText: 'Đồng ý',
      cancelText: 'Hủy',
      onOk: () => {
        triggerUpdateMutation.mutate();
      },
    });
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'gold', icon: <ClockCircleOutlined /> },
      success: { color: 'success', icon: <CheckCircleOutlined /> },
      error: { color: 'error', icon: <ExclamationCircleOutlined /> },
      partial: { color: 'warning', icon: <ExclamationCircleOutlined /> },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {status === 'pending' && 'Đang xử lý'}
        {status === 'success' && 'Thành công'}
        {status === 'error' && 'Lỗi'}
        {status === 'partial' && 'Thành công một phần'}
      </Tag>
    );
  };

  const getTriggerTag = (triggeredBy: string) => {
    const colors = {
      manual: 'blue',
      scheduled: 'purple',
    };

    const labels = {
      manual: 'Thủ công',
      scheduled: 'Tự động',
    };

    return (
      <Tag color={colors[triggeredBy as keyof typeof colors]}>
        {labels[triggeredBy as keyof typeof labels] || triggeredBy}
      </Tag>
    );
  };

  const columns = [
    {
      title: 'Thời gian',
      dataIndex: 'formatted_started_at',
      key: 'started_at',
      width: 150,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'Kích hoạt',
      dataIndex: 'triggered_by',
      key: 'triggered_by',
      width: 100,
      render: (triggeredBy: string) => getTriggerTag(triggeredBy),
    },
    {
      title: 'Sản phẩm',
      key: 'products',
      width: 120,
      render: (record: InventoryUpdateLog) => (
        <span>
          {record.products_updated}/{record.products_scraped}
        </span>
      ),
    },
    {
      title: 'Thời gian thực thi',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
    },
    {
      title: 'Lỗi',
      dataIndex: 'error_message',
      key: 'error_message',
      render: (error: string) => error ? (
        <span style={{ color: '#ff4d4f', fontSize: '12px' }}>
          {error.length > 50 ? `${error.substring(0, 50)}...` : error}
        </span>
      ) : '-',
    },
  ];

  const stats: InventoryUpdateStats = statsData || {
    total_updates: 0,
    successful_updates: 0,
    failed_updates: 0,
    manual_triggers: 0,
    scheduled_triggers: 0,
    avg_products_updated: 0,
    avg_execution_time: 0,
  };

  return (
    <Modal
      title="Cập nhật tồn kho từ Arito"
      open={open}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key="close" onClick={onClose}>
          Đóng
        </Button>,
        <Button
          key="refresh"
          icon={<ReloadOutlined />}
          onClick={() => {
            refetchLogs();
            queryClient.invalidateQueries({ queryKey: ['inventory-stats'] });
          }}
        >
          Refresh
        </Button>,
        <Button
          key="trigger"
          type="primary"
          icon={<ReloadOutlined />}
          loading={triggerUpdateMutation.isPending}
          onClick={handleTriggerUpdate}
        >
          Cập nhật ngay
        </Button>,
      ]}
    >
      <div style={{ marginBottom: 24 }}>
        <Alert
          message="Hướng dẫn sử dụng"
          description={
            <div>
              <p><strong>Tính năng này cho phép bạn:</strong></p>
              <ul style={{ marginBottom: 12, paddingLeft: 20 }}>
                <li>Xem lịch sử cập nhật tồn kho từ Arito</li>
                <li>Kích hoạt cập nhật thủ công khi cần thiết</li>
              </ul>
              <p><strong>📅 Lịch tự động:</strong></p>
              <ul style={{ marginBottom: 12, paddingLeft: 20 }}>
                <li>Hệ thống <strong>tự động cập nhật mỗi 2 tiếng</strong> vào khung giờ chẵn:</li>
                <li>2:00AM - 4:00AM - 6:00AM - 8:00AM - ... - 10:00PM - 12:00AM</li>
                <li><strong>Tránh cập nhật thủ công trong khung giờ này</strong> để không bị xung đột</li>
              </ul>
              <p><strong>⚠️ Lưu ý quan trọng:</strong></p>
              <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                <li><strong>Chỉ nhấn "Cập nhật ngay" một lần</strong> và đợi kết quả</li>
                <li>Quá trình cập nhật có thể mất <strong>10-15 phút</strong> tùy theo số lượng sản phẩm</li>
                <li><strong>Không nhấn lại</strong> trong khi đang xử lý để tránh tạo nhiều tác vụ trùng lặp</li>
                <li>Theo dõi trạng thái trong bảng lịch sử bên dưới</li>
              </ul>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* Statistics */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Statistic
              title="Tổng cập nhật (30 ngày)"
              value={stats.total_updates}
              loading={statsLoading}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Thành công"
              value={stats.successful_updates}
              valueStyle={{ color: '#3f8600' }}
              loading={statsLoading}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Thất bại"
              value={stats.failed_updates}
              valueStyle={{ color: '#cf1322' }}
              loading={statsLoading}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="TB sản phẩm/lần"
              value={Math.round(stats.avg_products_updated)}
              loading={statsLoading}
            />
          </Col>
        </Row>
      </div>

      {/* Logs Table */}
      <Table
        columns={columns}
        dataSource={logsData?.results || []}
        loading={logsLoading}
        rowKey="id"
        pagination={{
          total: logsData?.count || 0,
          pageSize: 10,
          showSizeChanger: false,
          showQuickJumper: false,
        }}
        size="small"
        scroll={{ x: 800 }}
      />
    </Modal>
  );
};
