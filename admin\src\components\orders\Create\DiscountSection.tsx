import { Checkbox, Input, Form } from "antd";

interface DiscountSectionProps {
  discountEnabled: boolean;
  onDiscountEnabledChange: (enabled: boolean) => void;
  discountAmount: number;
  onDiscountAmountChange: (value: number) => void;
  subtotal: number;
}

export function DiscountSection({
  discountEnabled,
  onDiscountEnabledChange,
  discountAmount,
  onDiscountAmountChange,
  subtotal,
}: DiscountSectionProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow space-y-4">
      <h2 className="text-xl font-semibold mb-4">Giảm giá</h2>
      <Form.Item className="mb-0">
        <Checkbox
          checked={discountEnabled}
          onChange={(e) => onDiscountEnabledChange(e.target.checked)}
        >
          Áp dụng giảm giá
        </Checkbox>
      </Form.Item>
      {discountEnabled && (
        <>
          <Form.Item label="Số tiền giảm giá (VNĐ)">
            <Input
              type="text"
              value={discountAmount.toLocaleString("vi-VN")}
              onChange={(e) => {
                const value = e.target.value.replace(/[^\d]/g, "");
                onDiscountAmountChange(Number(value) || 0);
              }}
              className="w-full"
              min={0}
              max={subtotal}
              placeholder="Nhập số tiền giảm giá"
            />
          </Form.Item>
          <div className="text-sm text-gray-600">
            Tổng tiền trước giảm giá: {subtotal.toLocaleString("vi-VN")} VNĐ
          </div>
          <div className="text-sm text-gray-600">
            Tổng tiền sau giảm giá:{" "}
            {Math.max(0, subtotal - discountAmount).toLocaleString("vi-VN")} VNĐ
          </div>
        </>
      )}
    </div>
  );
}
