# Warehouse Staff Permissions Update - 2025-01-08

## <PERSON><PERSON><PERSON> tiêu

<PERSON>p nhật quyền cho nhân viên kho (warehouse_staff) để có thể cập nhật đơn hàng từ "đang xử lý" → "hoàn thành" (processing → delivered)

## Yêu cầu

- [x] Nhân viên kế toán: chỉ được cập nhật từ "accounting_processing" → "processing" (đã đúng)
- [x] Nhân viên kho: thêm quyền cập nhật từ "processing" → "delivered" (ngoài quyền hiện tại "processing" → "shipped")

## Plan thực hiện

### File 1: src/components/orders/OrderCardShippingInfo.tsx

- [x] Thêm logic `canWarehouseUpdateToDelivered` cho warehouse_staff
- [x] Thêm button "Hoàn thành" cho warehouse_staff khi order ở trạng thái "processing"
- [x] Thêm handler `handleWarehouseDeliveredUpdate`

### File 2: src/components/orders/OrderTableView.tsx

- [x] Thêm button "Hoàn thành" cho warehouse_staff trong bảng
- [x] Logic tương tự như delivery_staff nhưng cho warehouse_staff

### File 3: src/pages/orders/kanban.tsx

- [x] Thêm function validateStatusTransition để kiểm tra quyền drag-drop
- [x] Thêm logic validation trong onDragEnd
- [x] Warehouse staff có thể drag từ "processing" → "shipped" hoặc "delivered"
- [x] Accounting staff có thể drag từ "accounting_processing" → "processing"
- [x] Tất cả roles có thể cancel orders

## Tiến độ

- [x] Bắt đầu chỉnh sửa
- [x] File 1 hoàn thành
- [x] File 2 hoàn thành
- [x] File 3 kiểm tra
- [x] Test và verify
- [x] Hoàn thành

## Tóm tắt thay đổi

### Thay đổi chính:

1. **OrderCardShippingInfo.tsx**: Thêm logic và button cho warehouse_staff cập nhật từ "processing" → "delivered"
2. **OrderTableView.tsx**: Thêm button "Hoàn thành" cho warehouse_staff trong bảng orders
3. **kanban.tsx**: Thêm validation quyền cho drag-drop trong kanban board

### Chi tiết thay đổi:

- Thêm biến `canWarehouseUpdateToDelivered` để kiểm tra quyền
- Thêm handler `handleWarehouseDeliveredUpdate` để xử lý cập nhật
- Thêm button "Hoàn thành" với màu xanh lá (bg-green-500) cho warehouse_staff
- Thêm function `validateStatusTransition` trong kanban để kiểm tra quyền drag-drop
- Warehouse_staff giờ có 2 lựa chọn khi order ở trạng thái "processing":
  - "Đã xuất kho" (chuyển sang "shipped")
  - "Hoàn thành" (chuyển sang "delivered")
- Kanban board giờ validate quyền trước khi cho phép drag-drop

### Kết quả:

✅ Nhân viên kho giờ có thể cập nhật đơn hàng từ "đang xử lý" → "hoàn thành"
✅ Vẫn giữ nguyên quyền cập nhật từ "đang xử lý" → "đang giao hàng"
✅ Kanban board có validation quyền cho drag-drop operations
✅ Tất cả roles có thể cancel orders trong kanban

## Git Commit Message

```
feat: add warehouse staff permission to update orders from processing to delivered

- Add canWarehouseUpdateToDelivered logic in OrderCardShippingInfo
- Add handleWarehouseDeliveredUpdate handler function
- Add "Hoàn thành" button for warehouse staff in order cards
- Add "Hoàn thành" button for warehouse staff in order table
- Add validateStatusTransition function in kanban for drag-drop permissions
- Warehouse staff now can update orders: processing → shipped OR processing → delivered
- Kanban board now validates permissions before allowing drag-drop operations
- Maintain existing accounting staff permissions (accounting_processing → processing)
- All roles can cancel orders in kanban board
```
