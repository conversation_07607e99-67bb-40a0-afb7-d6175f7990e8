import { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiCall, endpoints } from "@/lib/api";
import { ProductListItem } from "@/types/product";

export interface ChainProductItem extends ProductListItem {
  quantity: number;
  total_price: number;
  unit?: string;
  weight?: number;
}

interface UseChainProductTableProps {
  onProductsChange: (products: ChainProductItem[]) => void;
  pageSize?: number;
}

interface UseChainProductTableReturn {
  // Data
  products: ChainProductItem[];
  data: { results: ProductListItem[]; count: number } | undefined;
  
  // Loading states
  isLoading: boolean;
  isFetching: boolean;
  
  // Search & Pagination
  searchQuery: string;
  currentPage: number;
  
  // Calculated values
  totalAmount: number;
  selectedCount: number;
  totalSelectedAcrossAllPages: number;
  startIndex: number;
  endIndex: number;
  
  // Handlers
  handleQuantityChange: (productId: number, quantity: number) => void;
  handleSearch: (value: string) => void;
  handleReset: () => void;
  handlePageChange: (page: number) => void;
  handleClearAll: () => void;
}

export function useChainProductTable({ 
  onProductsChange, 
  pageSize = 10 
}: UseChainProductTableProps): UseChainProductTableReturn {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [productQuantities, setProductQuantities] = useState<Record<number, number>>({});
  const [selectedProductsData, setSelectedProductsData] = useState<Record<number, ProductListItem>>({});

  // Use TanStack Query to fetch chain products
  const { data, isLoading, isFetching } = useQuery<{
    results: ProductListItem[];
    count: number;
  }>({
    queryKey: ["chain-products", currentPage, searchQuery],
    queryFn: () => {
      let url = `${endpoints.products.list}?page=${currentPage}&page_size=${pageSize}&is_chain=true&is_active=true`;

      if (searchQuery.trim()) {
        url += `&search=${searchQuery.trim()}`;
      }

      return apiCall("GET", url);
    },
  });

  // Convert API data to ChainProductItem format
  const products = useMemo(() => {
    if (!data?.results) return [];

    return data.results.map((product: ProductListItem): ChainProductItem => ({
      ...product,
      quantity: productQuantities[product.id] || 0,
      total_price: (productQuantities[product.id] || 0) * (product.chain_price || 0),
    }));
  }, [data?.results, productQuantities]);

  // Handlers
  const handleQuantityChange = (productId: number, quantity: number) => {
    // Update quantity tracking
    const updatedQuantities = {
      ...productQuantities,
      [productId]: quantity,
    };
    setProductQuantities(updatedQuantities);

    // Update selected products data
    const currentProduct = data?.results.find(p => p.id === productId);
    if (currentProduct) {
      if (quantity > 0) {
        setSelectedProductsData(prev => ({
          ...prev,
          [productId]: currentProduct
        }));
      } else {
        setSelectedProductsData(prev => {
          const newData = { ...prev };
          delete newData[productId];
          return newData;
        });
      }
    }

    // Build complete list of selected products
    const allSelectedProducts: ChainProductItem[] = [];
    
    // Add products from current page
    products.forEach(product => {
      const qty = product.id === productId ? quantity : (updatedQuantities[product.id] || 0);
      if (qty > 0) {
        allSelectedProducts.push({
          ...product,
          quantity: qty,
          total_price: qty * (product.chain_price || 0),
        });
      }
    });

    // Add products from other pages that have quantities
    Object.entries(updatedQuantities).forEach(([id, qty]) => {
      const productId = parseInt(id);
      if (qty > 0 && !products.find(p => p.id === productId)) {
        const productData = selectedProductsData[productId];
        if (productData) {
                  allSelectedProducts.push({
          ...productData,
          quantity: qty,
          total_price: qty * (productData.chain_price || 0),
        });
        }
      }
    });

    onProductsChange(allSelectedProducts);
  };

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleReset = () => {
    setSearchQuery("");
    setCurrentPage(1); // Reset to first page when clearing search
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleClearAll = () => {
    setProductQuantities({});
    setSelectedProductsData({});
    onProductsChange([]);
  };

  // Calculate totals for current page
  const totalAmount = products.reduce((sum, product) => sum + product.total_price, 0);
  const selectedCount = products.filter(p => p.quantity > 0).length;

  // Calculate total selected across all pages
  const totalSelectedAcrossAllPages = Object.values(productQuantities).filter(qty => qty > 0).length;

  // Calculate pagination info
  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, data?.count || 0);

  return {
    // Data
    products,
    data,
    
    // Loading states
    isLoading,
    isFetching,
    
    // Search & Pagination
    searchQuery,
    currentPage,
    
    // Calculated values
    totalAmount,
    selectedCount,
    totalSelectedAcrossAllPages,
    startIndex,
    endIndex,
    
    // Handlers
    handleQuantityChange,
    handleSearch,
    handleReset,
    handlePageChange,
    handleClearAll,
  };
}
