import { ReactNode, useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/context/auth-hooks";
import { Menu } from "antd";
import {
  MenuUnfoldOutlined,
  LogoutOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { useResponsive } from "@/hooks/useResponsive";

interface MobileTabletLayoutProps {
  children: ReactNode;
  menuItems: any[];
}

export function MobileTabletLayout({ children, menuItems }: MobileTabletLayoutProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { isMobile, isTablet } = useResponsive();

  // Close mobile menu when location changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  // Handle escape key to close mobile menu
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener("keydown", handleEscapeKey);
    return () => document.removeEventListener("keydown", handleEscapeKey);
  }, [mobileMenuOpen]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if ((isMobile || isTablet) && mobileMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isMobile, isTablet, mobileMenuOpen]);

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const userRole = user?.is_superuser ? "Admin" : user?.is_staff ? "Staff" : "";

  const onMenuClick = (item: any) => {
    navigate(item.key);
    if (isMobile || isTablet) {
      setMobileMenuOpen(false);
    }
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };

  // Mobile Layout
  if (isMobile) {
    return (
      <>
        {/* Mobile Header */}
        <div className="sticky top-0 z-[100] bg-white border-b border-gray-200 px-3 flex items-center justify-between min-h-[60px] shadow-sm">
          <button
            className="min-w-[44px] min-h-[44px] border-0 bg-transparent flex items-center justify-center rounded-md cursor-pointer transition-colors duration-200 hover:bg-gray-100 active:bg-gray-100"
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
          >
            <MenuUnfoldOutlined />
          </button>
          <h1 className="text-lg font-semibold text-gray-900 m-0">3T Admin</h1>
          <button
            className="min-w-[44px] min-h-[44px] border-0 bg-transparent flex items-center justify-center rounded-md cursor-pointer transition-colors duration-200 hover:bg-gray-100 active:bg-gray-100"
            onClick={handleLogout}
            aria-label="Logout"
          >
            <LogoutOutlined />
          </button>
        </div>

        {/* Mobile Sidebar Overlay */}
        <div
          className={`fixed top-0 left-0 w-screen h-screen bg-black bg-opacity-50 z-[999] transition-opacity duration-300 ${
            mobileMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
          }`}
          onClick={closeMobileMenu}
          aria-hidden="true"
        />

        {/* Mobile Sidebar */}
        <div className={`fixed top-0 left-0 w-[280px] h-screen bg-white shadow-lg z-[1000] overflow-y-auto transition-transform duration-300 ease-in-out ${
          mobileMenuOpen ? "transform-none" : "-translate-x-full"
        }`}>
          <div className="h-16 flex items-center justify-between px-4 border-b border-solid border-gray-200">
            <h2 className="m-0 text-xl font-bold">3T Admin</h2>
            <button
              className="min-w-[44px] min-h-[44px] border-0 bg-transparent flex items-center justify-center rounded-md cursor-pointer transition-colors duration-200 hover:bg-gray-100 active:bg-gray-100"
              onClick={closeMobileMenu}
              aria-label="Close menu"
            >
              <CloseOutlined />
            </button>
          </div>

          {/* User Info */}
          {user && (
            <div className="p-4 border-b border-solid border-gray-200">
              <div className="text-sm font-medium">{user.email}</div>
              <div className="text-xs text-gray-500">{userRole}</div>
            </div>
          )}

          {/* Mobile Menu */}
          <Menu
            mode="inline"
            defaultOpenKeys={["orders"]}
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={onMenuClick}
            className="border-0"
            style={{ background: "transparent" }}
          />
        </div>

        {/* Mobile Content */}
        <div className="p-3">
          {children}
        </div>
      </>
    );
  }

  // Tablet Layout
  if (isTablet) {
    return (
      <>
        {/* Tablet Header */}
        <div className="sticky top-0 z-[100] bg-white border-b border-gray-200 px-4 flex items-center justify-between min-h-[60px] shadow-sm">
          <button
            className="min-w-[44px] min-h-[44px] border-0 bg-transparent flex items-center justify-center rounded-md cursor-pointer transition-colors duration-200 hover:bg-gray-100 active:bg-gray-100"
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
          >
            <MenuUnfoldOutlined />
          </button>
          <h1 className="text-lg font-semibold text-gray-900 m-0">3T Admin</h1>
          <div className="flex items-center gap-4">
            {user && (
              <div className="flex flex-col items-end">
                <span className="text-sm">{user.email}</span>
                <span className="text-xs text-gray-500">{userRole}</span>
              </div>
            )}
            <button
              className="min-w-[44px] min-h-[44px] border-0 bg-transparent flex items-center justify-center rounded-md cursor-pointer transition-colors duration-200 hover:bg-gray-100 active:bg-gray-100"
              onClick={handleLogout}
              aria-label="Logout"
            >
              <LogoutOutlined />
            </button>
          </div>
        </div>

        {/* Tablet Sidebar Overlay */}
        <div
          className={`fixed top-0 left-0 w-screen h-screen bg-black bg-opacity-50 z-[999] transition-opacity duration-300 ${
            mobileMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
          }`}
          onClick={closeMobileMenu}
          aria-hidden="true"
        />

        {/* Tablet Sidebar */}
        <div className={`fixed top-0 left-0 w-[320px] h-screen bg-white shadow-lg z-[1000] overflow-y-auto transition-transform duration-300 ease-in-out ${
          mobileMenuOpen ? "transform-none" : "-translate-x-full"
        }`}>
          <div className="h-16 flex items-center justify-between px-4 border-b border-solid border-gray-200">
            <h2 className="m-0 text-xl font-bold">3T Admin</h2>
            <button
              className="min-w-[44px] min-h-[44px] border-0 bg-transparent flex items-center justify-center rounded-md cursor-pointer transition-colors duration-200 hover:bg-gray-100 active:bg-gray-100"
              onClick={closeMobileMenu}
              aria-label="Close menu"
            >
              <CloseOutlined />
            </button>
          </div>

          {/* Menu */}
          <Menu
            mode="inline"
            defaultOpenKeys={["orders"]}
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={onMenuClick}
            className="border-0"
            style={{ background: "transparent" }}
          />
        </div>

        {/* Tablet Content */}
        <div className="p-6">
          {children}
        </div>
      </>
    );
  }

  // This component only handles mobile and tablet, desktop returns null
  return null;
}
