import { OrderItem } from "@/types/order";

export function calculateTotalWeight(items: OrderItem[]): number {
  return items.reduce((totalWeight, item) => {
    const itemWeight = Number(item.product_weight) || 0;
    const quantity = Number(item.quantity) || 0;
    return totalWeight + (itemWeight * quantity);
  }, 0);
}

export function formatWeight(weight: number): string {
  if (weight <= 0) return 'N/A';
  
  // Round to 2 decimal places and remove trailing zeros
  const roundedWeight = Math.round(weight * 100) / 100;
  return `${roundedWeight} kg`;
}

export function isValidWeight(weight: number | undefined | null): boolean {
  return typeof weight === 'number' && weight >= 0 && !isNaN(weight);
}
