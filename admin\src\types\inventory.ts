/**
 * Inventory Update Types
 * TypeScript definitions for inventory update logs and related data
 */

export interface InventoryUpdateLog {
  id: number;
  started_at: string;
  completed_at: string | null;
  status: 'pending' | 'success' | 'error' | 'partial';
  status_display: string;
  triggered_by: 'manual' | 'scheduled';
  triggered_by_display: string;
  products_scraped: number;
  products_updated: number;
  products_not_found: number;
  pages_processed: number;
  error_message: string | null;
  error_details: Record<string, any>;
  execution_time_seconds: number | null;
  scraping_summary: Record<string, any>;
  duration: string | null;
  success_rate: string | null;
  formatted_started_at: string | null;
  formatted_completed_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface InventoryUpdateLogSummary {
  id: number;
  started_at: string;
  completed_at: string | null;
  status: 'pending' | 'success' | 'error' | 'partial';
  status_display: string;
  triggered_by: 'manual' | 'scheduled';
  triggered_by_display: string;
  products_scraped: number;
  products_updated: number;
  products_not_found: number;
  execution_time_seconds: number | null;
  duration: string | null;
  formatted_started_at: string | null;
  error_message: string | null;
}

export interface InventoryUpdateStats {
  total_updates: number;
  successful_updates: number;
  failed_updates: number;
  manual_triggers: number;
  scheduled_triggers: number;
  avg_products_updated: number;
  avg_execution_time: number;
}

export interface InventoryUpdateStatus {
  latest_update: {
    id: number;
    started_at: string;
    completed_at: string | null;
    status: string;
    triggered_by: string;
    products_updated: number;
    products_scraped: number;
    execution_time: number | null;
    error_message: string | null;
  };
  has_pending: boolean;
  pending_update: {
    id: number;
    started_at: string;
    triggered_by: string;
  } | null;
}

export interface TriggerUpdateResponse {
  status: 'success' | 'error';
  message: string;
  triggered_by?: string;
  user?: string;
}

export interface InventoryLogListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: InventoryUpdateLog[];
}

// Filter options for inventory logs
export interface InventoryLogFilters {
  status?: 'pending' | 'success' | 'error' | 'partial';
  triggered_by?: 'manual' | 'scheduled';
  date_from?: string;
  date_to?: string;
  page?: number;
  page_size?: number;
}
