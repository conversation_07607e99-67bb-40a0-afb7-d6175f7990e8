import { Order } from "@/types/order";
import { CustomerRankBadge } from "@/components/customers/CustomerRankBadge";

interface MobileTabletCustomerInfoProps {
  order: Order;
  isMobile: boolean;
}

export function MobileTabletCustomerInfo({ order, isMobile }: MobileTabletCustomerInfoProps) {
  if (isMobile) {
    return (
      <div className="mb-6 bg-white rounded-lg border p-4">
        <h3 className="text-lg font-semibold mb-4">Thông tin khách hàng</h3>
        
        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-1">Tên khách hàng</p>
          <div className="flex flex-col gap-2">
            <p className="font-medium text-base">{order.user.full_name}</p>
            <div className="flex">
              <CustomerRankBadge
                rank={order.user.rank || 'normal'}
                size="default"
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-1">Email</p>
          <p className="font-medium break-all">{order.email}</p>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-1">Số điện thoại</p>
          <p className="font-medium">{order.phone_number}</p>
        </div>

        <div>
          <p className="text-sm text-gray-600 mb-1">Địa chỉ giao hàng</p>
          <p className="font-medium leading-relaxed">
            {[
              order.shipping_address,
              order.ward ? `Phường ${order.ward}` : null,
              order.district ? `Quận ${order.district}` : null,
              order.city ? `Thành phố ${order.city}` : null,
            ]
              .filter(Boolean)
              .join(", ")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6 bg-white rounded-lg border p-6">
      <h3 className="text-lg font-semibold mb-4">Thông tin khách hàng</h3>
      
      <div className="grid grid-cols-1 gap-4">
        <div className="col-span-1">
          <p className="text-sm text-gray-600 mb-2">Tên khách hàng</p>
          <div className="flex items-center gap-3">
            <p className="font-medium text-base">{order.user.full_name}</p>
            <CustomerRankBadge
              rank={order.user.rank || 'normal'}
              size="default"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600 mb-1">Email</p>
            <p className="font-medium text-sm break-all">{order.email}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600 mb-1">Số điện thoại</p>
            <p className="font-medium">{order.phone_number}</p>
          </div>
        </div>

        <div className="col-span-1">
          <p className="text-sm text-gray-600 mb-2">Địa chỉ giao hàng</p>
          <p className="font-medium leading-relaxed">
            {[
              order.shipping_address,
              order.ward ? `Phường ${order.ward}` : null,
              order.district ? `Quận ${order.district}` : null,
              order.city ? `Thành phố ${order.city}` : null,
            ]
              .filter(Boolean)
              .join(", ")}
          </p>
        </div>
      </div>
    </div>
  );
}
