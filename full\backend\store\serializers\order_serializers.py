"""
Order-related serializers
"""
from decimal import Decimal, InvalidOperation
from rest_framework import serializers
from django.utils import timezone
from django.contrib.auth.models import User
from django.db import transaction
from ..models import Order, OrderItem, Product, ProductVariant, OrderPromotion, Promotion, PromotionUsage
from .base_serializers import UserSerializer
from .cart_serializers import PromotionSerializer
from ..utils.voucher_validation import validate_order_promotions

class OrderItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.code', read_only=True, allow_null=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True, allow_null=True)
    product_weight = serializers.DecimalField(source='product.weight', max_digits=15, decimal_places=2, read_only=True, allow_null=True)
    total_price = serializers.SerializerMethodField()
    unit = serializers.SerializerMethodField()
    product_image_url = serializers.SerializerMethodField()
    variant_image_url = serializers.SerializerMethodField()
    chiet_khau_amount = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True, allow_null=True)

    def get_unit(self, obj):
        """Get unit from variant if available, otherwise from product"""
        if obj.variant and obj.variant.unit:
            return obj.variant.unit
        return obj.product.unit

    def get_total_price(self, obj):
        """Get total price with product discount applied"""
        return obj.get_total_price_with_discount()

    def _get_absolute_url(self, image_field):
        """Helper method to build absolute URL from an image field"""
        if image_field:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(image_field.url)
            return image_field.url
        return None

    def get_product_image_url(self, obj):
        """Get product main image URL"""
        return self._get_absolute_url(obj.product.main_image)

    def get_variant_image_url(self, obj):
        """Get variant main image URL if variant exists"""
        if obj.variant:
            return self._get_absolute_url(obj.variant.main_image)
        return None

    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'product_name', 'product_code', 'variant', 'variant_name', 'quantity', 'price', 'total_price', 'product_weight', 'unit', 'product_image_url', 'variant_image_url', 'chiet_khau_amount']
        read_only_fields = ['id']

class OrderItemUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating order items with total_price support"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.code', read_only=True, allow_null=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True, allow_null=True)
    product_weight = serializers.DecimalField(source='product.weight', max_digits=15, decimal_places=2, read_only=True, allow_null=True)
    unit = serializers.SerializerMethodField()
    total_price = serializers.DecimalField(max_digits=15, decimal_places=2, required=False, allow_null=True)
    chiet_khau_amount = serializers.DecimalField(max_digits=15, decimal_places=2, required=False, allow_null=True)
    product_image_url = serializers.SerializerMethodField()
    variant_image_url = serializers.SerializerMethodField()

    def get_unit(self, obj):
        """Get unit from variant if available, otherwise from product"""
        if obj.variant and obj.variant.unit:
            return obj.variant.unit
        return obj.product.unit

    def _get_absolute_url(self, image_field):
        """Helper method to build absolute URL from an image field"""
        if image_field:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(image_field.url)
            return image_field.url
        return None

    def get_product_image_url(self, obj):
        """Get product main image URL"""
        return self._get_absolute_url(obj.product.main_image)

    def get_variant_image_url(self, obj):
        """Get variant main image URL if variant exists"""
        if obj.variant:
            return self._get_absolute_url(obj.variant.main_image)
        return None

    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'product_name', 'product_code', 'variant', 'variant_name', 'quantity', 'price', 'total_price', 'product_weight', 'unit', 'product_image_url', 'variant_image_url', 'chiet_khau_amount']
        read_only_fields = ['id']

class OrderUpdateSerializer(serializers.ModelSerializer):
    items = OrderItemUpdateSerializer(many=True, required=False)

    def validate_items(self, value):
        if value is not None:
            for item in value:
                # Handle both 'price' and 'total_price' fields
                if 'price' in item and item['price'] is not None:
                    try:
                        item['price'] = Decimal(str(item['price'])).quantize(Decimal('0.01'))
                    except (TypeError, ValueError, InvalidOperation):
                        raise serializers.ValidationError("Invalid price format")

                if 'chiet_khau_amount' in item and item['chiet_khau_amount'] is not None:
                    try:
                        item['chiet_khau_amount'] = Decimal(str(item['chiet_khau_amount'])).quantize(Decimal('0.01'))
                    except (TypeError, ValueError, InvalidOperation):
                        raise serializers.ValidationError("Invalid chiet_khau_amount format")

                if 'total_price' in item and item['total_price'] is not None:
                    try:
                        # Keep total_price for later use in update method
                        # Convert to Decimal for validation
                        item['total_price'] = Decimal(str(item['total_price']))

                        # Also calculate unit price for fallback
                        quantity = item.get('quantity', 1)
                        if quantity <= 0:
                            raise serializers.ValidationError("Quantity must be greater than 0")
                        item['calculated_price'] = (item['total_price'] / Decimal(quantity)).quantize(Decimal('0.01'))
                    except (TypeError, ValueError, InvalidOperation):
                        raise serializers.ValidationError("Invalid total_price format")
        return value

    class Meta:
        model = Order
        fields = [
            'shipping_address', 'ward', 'district', 'city', 'phone_number', 'email',
            'delivery_date', 'delivery_time', 'shipping_unit', 'shipping_fee',
            'payment_method', 'payment_status', 'company_payment_received',
            'status', 'notes', 'sales_admin', 'delivery_staff',
            'discount', 'tax', 'have_tax', 'items', 'showroom_status', 'is_printed', 'is_chain'
        ]
        extra_kwargs = {
            'shipping_address': {'required': False},
            'phone_number': {'required': False},
            'email': {'required': False},
            'ward': {'required': False},
            'district': {'required': False},
            'city': {'required': False},
            'delivery_date': {'required': False},
            'delivery_time': {'required': False},
            'shipping_unit': {'required': False},
            'shipping_fee': {'required': False},
            'payment_method': {'required': False},
            'payment_status': {'required': False},
            'sales_admin': {'required': False},
            'delivery_staff': {'required': False},
            'discount': {'required': False},
            'tax': {'required': False},
            'items': {'required': False}
        }

    def update(self, instance, validated_data):
        items_data = validated_data.pop('items', None)

        # Auto-set have_tax based on tax value
        if 'tax' in validated_data:
            tax_value = validated_data.get('tax', 0)
            if tax_value > 0:
                validated_data['have_tax'] = True
            else:
                validated_data['have_tax'] = False

        # Update other fields first (including have_tax)
        instance = super().update(instance, validated_data)

        # Update order items if provided
        if items_data is not None:
            # Clear existing items if new items are provided
            instance.items.all().delete()

            # Calculate new total price
            total_price = 0

            # Create new items
            for item_data in items_data:
                product = item_data['product']  # Already a Product instance
                quantity = item_data['quantity']
                variant = item_data.get('variant')  # Already a ProductVariant instance if provided

                # Determine price based on custom price or product default
                # When updating chiet_khau_amount, preserve the original price
                if 'price' in item_data and item_data['price'] is not None:
                    price = item_data['price']  # Already converted to Decimal in validate_items
                    total_price += price * quantity
                else:
                    # Use default product/variant price
                    if variant:
                        if variant.discount_price:
                            price = variant.discount_price
                        elif variant.price:
                            price = variant.price
                        elif product.discount_price:
                            price = product.discount_price
                        else:
                            price = product.price
                    else:
                        price = product.discount_price or product.price
                    total_price += price * quantity

                # Calculate total_price for this item (price * quantity)
                item_total_price = price * quantity

                # Create OrderItem
                order_item_data = {
                    'order': instance,
                    'product': product,
                    'variant': variant,
                    'quantity': quantity,
                    'price': price,
                    'total_price': item_total_price  # Store exact total_price if provided
                }
                
                # Add chiet_khau_amount if provided in the request
                if 'chiet_khau_amount' in item_data and item_data['chiet_khau_amount'] is not None:
                    order_item_data['chiet_khau_amount'] = item_data['chiet_khau_amount']

                OrderItem.objects.create(**order_item_data)

            # Update order total price and save (this will trigger the save() method with correct have_tax value)
            instance.total_price = total_price
            instance.save()

        return instance

class OrderPromotionSerializer(serializers.ModelSerializer):
    """Serializer for OrderPromotion model"""
    promotion = PromotionSerializer(read_only=True)
    code = serializers.CharField(write_only=True)

    class Meta:
        model = OrderPromotion
        fields = ['id', 'promotion', 'discount_amount', 'applied_at', 'code']
        read_only_fields = ['id', 'applied_at', 'promotion', 'discount_amount']

    def validate_code(self, value):
        try:
            promotion = Promotion.objects.get(code=value, is_active=True)
            if not promotion.is_valid:
                raise serializers.ValidationError("This promotion code has expired or reached its usage limit.")

            # Check per-user usage limit (only for authenticated users with limited vouchers)
            # For unauthenticated users, validation will happen during order creation
            request = self.context.get('request')
            if request and request.user.is_authenticated and promotion.usage_limit_per_user is not None:
                if not promotion.can_user_use_promotion(request.user):
                    raise serializers.ValidationError(f"You have already used this promotion {promotion.usage_limit_per_user} time(s). Each user can only use this promotion {promotion.usage_limit_per_user} time(s).")
        except Promotion.DoesNotExist:
            raise serializers.ValidationError("Invalid or inactive promotion code.")
        return value

class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    user = UserSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    sales_admin = UserSerializer(read_only=True)
    delivery_staff = UserSerializer(read_only=True)
    payment_method_display = serializers.CharField(source='get_payment_method_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    shipping_unit_display = serializers.CharField(source='get_shipping_unit_display', read_only=True)
    showroom_status_display = serializers.CharField(source='get_showroom_status_display', read_only=True)
    promotions = OrderPromotionSerializer(many=True, read_only=True)
    discount_total = serializers.SerializerMethodField()
    final_total = serializers.SerializerMethodField()
    subtotal_with_product_discounts = serializers.SerializerMethodField()
    total_product_discount = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'user', 'status', 'status_display', 'total_price',
            'shipping_address', 'ward', 'district', 'city', 'phone_number', 'email',
            'sales_admin', 'delivery_staff',
            'delivery_date', 'delivery_time', 'shipping_unit', 'shipping_unit_display', 'shipping_fee',
            'payment_method', 'payment_method_display', 'payment_status', 'payment_status_display',
            'company_payment_received', 'notes',
            'created_at', 'updated_at', 'confirmation_time', 'completion_time',
            'items', 'promotions', 'discount_total', 'final_total', 'subtotal_with_product_discounts', 'total_product_discount',
            'discount', 'tax', 'have_tax', 'is_showroom', 'showroom_status', 'showroom_status_display',
            'is_printed', 'is_e_comm', 'is_chain'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'total_price', 'status_display',
            'payment_method_display', 'payment_status_display', 'shipping_unit_display'
        ]

    def get_discount_total(self, obj):
        return sum(op.discount_amount for op in obj.promotions.all())

    def get_final_total(self, obj):
        return max(0, obj.get_final_price())

    def get_subtotal_with_product_discounts(self, obj):
        """Get subtotal with product-level discounts applied"""
        return obj.get_subtotal_with_product_discounts()

    def get_total_product_discount(self, obj):
        """Calculate total product discount amount"""
        total_discount = 0
        for item in obj.items.all():
            base_total = item.get_total_price()
            discounted_total = item.get_total_price_with_discount()
            total_discount += (base_total - discounted_total)
        return total_discount

class OrderCreateSerializer(serializers.ModelSerializer):
    user = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        required=False,
        write_only=True,
        help_text="User ID for whom the order is being created (staff only)"
    )
    sales_admin = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        required=False,
        write_only=True,
        help_text="User ID of the sales admin to assign to this order (optional)"
    )
    discount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0, required=False)
    tax = serializers.DecimalField(max_digits=5, decimal_places=2, default=0, required=False)
    items = serializers.ListField(
        child=serializers.DictField(),
        write_only=True,
        help_text="""List of items to order. Each item should contain:
            - product: ID of the product (required)
            - variant: ID of the variant (optional)
            - quantity: Number of items (required)
            - price: Custom unit price for the item (optional)
            - total_price: Custom total price for the item (optional)
            Note: If total_price is provided, it will override the price field and the unit price
            will be calculated as total_price/quantity. If neither price nor total_price is provided,
            the default product price will be used."""
    )
    promotion_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        write_only=True,
        help_text="List of promotion IDs to apply to the order"
    )

    status = serializers.ChoiceField(
        choices=Order.STATUS_CHOICES,
        default='pending',
        required=False,
        help_text="Order status (optional, defaults to 'pending')"
    )



    class Meta:
        model = Order
        fields = [
            'shipping_address', 'ward', 'district', 'city', 'phone_number', 'email',
            'delivery_date', 'delivery_time', 'shipping_unit', 'shipping_fee',
            'payment_method', 'payment_status', 'company_payment_received',
            'notes', 'items', 'user', 'sales_admin',
            'tax', 'discount', 'have_tax', 'status', 'promotion_ids', 'is_showroom', 'showroom_status', 'is_printed', 'is_e_comm', 'is_chain'
        ]

    @transaction.atomic
    def create(self, validated_data):
        items_data = validated_data.pop('items')
        promotion_ids = validated_data.pop('promotion_ids', [])
        request = self.context.get('request')

        # Auto-set have_tax based on tax value
        if 'tax' in validated_data:
            tax_value = validated_data.get('tax', 0)
            if tax_value > 0:
                validated_data['have_tax'] = True
            else:
                validated_data['have_tax'] = False

        # Calculate total price
        total_price = Decimal('0.00')
        order_items = []

        # First, validate stock availability for all items
        out_of_stock_products = []

        for item_data in items_data:
            # Get product
            try:
                product_id = item_data.get('product')
                product = Product.objects.get(id=product_id)
            except Product.DoesNotExist:
                raise serializers.ValidationError(f"Product with ID {product_id} does not exist")

            # Get variant if specified
            variant = None
            if 'variant' in item_data and item_data['variant']:
                try:
                    variant = ProductVariant.objects.get(id=item_data['variant'])
                except ProductVariant.DoesNotExist:
                    raise serializers.ValidationError(f"Variant with ID {item_data['variant']} does not exist")

            quantity = item_data['quantity']

            # Check stock availability
            available_stock = variant.stock if variant else product.stock
            if available_stock < quantity:
                product_name = f"{product.name} - {variant.name}" if variant else product.name
                if quantity == 1:
                    out_of_stock_products.append(f"• {product_name}: chỉ còn {available_stock} trong kho")
                else:
                    out_of_stock_products.append(f"• {product_name}: chỉ còn {available_stock} trong kho, bạn yêu cầu {quantity}")
                continue

            # Đơn giản hóa logic xử lý giá
            if variant:
                price = variant.discount_price or variant.price
            else:
                price = product.discount_price or product.price

            # Check if this is a showroom order or e-commerce order
            if validated_data.get('is_showroom', False) or validated_data.get('is_e_comm', False) or validated_data.get('is_chain', False):
                price = Decimal('0.00')
                # For showroom orders, don't add to total_price
            else:
                total_price += price * quantity

            # Store the processed item data
            order_items.append({
                'product': product,
                'variant': variant,
                'quantity': quantity,
                'price': price,
                'total_price': price * quantity,
                'chiet_khau_amount': item_data.get('chiet_khau_amount', 0)
            })

        # Check if there are any out of stock products and raise error with all of them
        if out_of_stock_products:
            error_message = "Các sản phẩm sau không đủ hàng trong kho:\n" + "\n".join(out_of_stock_products)
            raise serializers.ValidationError(error_message)

        # Set the user for the order (could be different from request.user if admin is creating for customer)
        order_user = validated_data.pop('user', request.user)

        # Get optional sales_admin from validated_data
        sales_admin = validated_data.pop('sales_admin', None)

        # If no sales_admin specified, use current logic
        if not sales_admin:
            sales_admin = request.user if order_user != request.user else None

        # Check if this is a chain order and set appropriate status
        if validated_data.get('is_chain', False):
            # For chain orders, set status to accounting_processing instead of pending
            validated_data['status'] = 'accounting_processing'

        # Create order
        order = Order.objects.create(
            user=order_user,
            total_price=total_price,
            sales_admin=sales_admin,
            **validated_data
        )

        # Create order items
        for item_data in order_items:
            OrderItem.objects.create(order=order, **item_data)

            # Update product stock
            product = item_data['product']
            variant = item_data['variant']
            quantity = item_data['quantity']

            if variant:
                # Update variant stock
                variant.stock -= quantity
                variant.save()
            else:
                # Update product stock
                product.stock -= quantity
                product.save()

        # Validate promotions for freeship voucher zone requirements
        if promotion_ids:
            validation_result = validate_order_promotions(order, promotion_ids)
            if not validation_result['valid']:
                # Transaction will automatically rollback all database changes
                raise serializers.ValidationError({'non_field_errors': [validation_result['error_message']]})

        # Apply promotions and calculate total discount if provided
        total_discount = Decimal('0.00')
        for promotion_id in promotion_ids:
            try:
                promotion = Promotion.objects.get(id=promotion_id, is_active=True)
                if promotion.is_valid and total_price >= promotion.min_purchase_amount:
                    if promotion.value == 0 and not promotion.is_percentage:
                        # Calculate discount amount with special handling for freeship vouchers
                        discount_amount = order.shipping_fee or Decimal('0.00')
                    else:
                        # Regular promotion - use standard calculation
                        discount_amount = promotion.calculate_discount(total_price)

                    # Check per-user usage limit before applying
                    if promotion.can_user_use_promotion(order.user):
                        OrderPromotion.objects.create(
                            order=order,
                            promotion=promotion,
                            discount_amount=discount_amount
                        )

                        # Create PromotionUsage record for tracking per-user usage
                        PromotionUsage.objects.create(
                            promotion=promotion,
                            order=order,
                            user=order.user,
                            discount_amount=discount_amount
                        )

                        promotion.usage_count += 1
                        promotion.save()
                        total_discount += discount_amount
            except Promotion.DoesNotExist:
                pass  # Silently ignore invalid promotion IDs during order creation

        # Update order's discount field with total discount from promotions
        if total_discount > 0:
            order.discount = total_discount
            order.save(update_fields=['discount'])


        return order
