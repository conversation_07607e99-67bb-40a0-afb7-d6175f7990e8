import { useMediaQuery } from '@mui/material';

// Enhanced breakpoints for admin responsive design
const breakpoints = {
  // Mobile devices (iPhone 11/12/13/14 Pro Max, Samsung Galaxy)
  mobile: 480,
  // Tablet devices (iPad, Android tablets)  
  tablet: 1024,
  // Desktop devices
  desktop: 1025,
  // Legacy Tailwind breakpoints for compatibility
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

export type Breakpoint = keyof typeof breakpoints;

// Export breakpoints for CSS usage
export { breakpoints };

/**
 * Enhanced responsive hooks using MUI's useMediaQuery for better performance and SSR support
 * These hooks provide more reliable responsive behavior compared to custom event listeners
 */

export interface ResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isSmallScreen: boolean; // Mobile + Tablet
  isLargeScreen: boolean; // Desktop+
}

/**
 * Primary responsive hook using MUI's useMediaQuery
 * Provides better performance and SSR compatibility than custom hooks
 */
export function useResponsive(): ResponsiveState {
  // Use exact breakpoints from existing system to maintain consistency
  const isMobile = useMediaQuery(`(max-width: ${breakpoints.mobile}px)`);
  const isTablet = useMediaQuery(
    `(min-width: ${breakpoints.mobile + 1}px) and (max-width: ${breakpoints.tablet}px)`
  );
  const isDesktop = useMediaQuery(`(min-width: ${breakpoints.desktop}px)`);
  
  // Computed states for convenience
  const isSmallScreen = isMobile || isTablet;
  const isLargeScreen = isDesktop;

  return {
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    isLargeScreen,
  };
}

/**
 * Individual breakpoint hooks using MUI's useMediaQuery
 * These can be used when you only need to check one specific breakpoint
 */

export function useMobile(): boolean {
  return useMediaQuery(`(max-width: ${breakpoints.mobile}px)`);
}

export function useTablet(): boolean {
  return useMediaQuery(
    `(min-width: ${breakpoints.mobile + 1}px) and (max-width: ${breakpoints.tablet}px)`
  );
}

export function useDesktop(): boolean {
  return useMediaQuery(`(min-width: ${breakpoints.desktop}px)`);
}

// Legacy aliases for backwards compatibility
export function useMobileMediaQuery(): boolean {
  return useMobile();
}

export function useTabletMediaQuery(): boolean {
  return useTablet();
}

export function useDesktopMediaQuery(): boolean {
  return useDesktop();
}

/**
 * Tailwind-compatible breakpoint hooks
 * These match Tailwind's responsive system for easier migration
 */

export function useSmallScreenMediaQuery(): boolean {
  return useMediaQuery('(max-width: 640px)'); // Tailwind sm
}

export function useMediumScreenMediaQuery(): boolean {
  return useMediaQuery('(min-width: 768px)'); // Tailwind md
}

export function useLargeScreenMediaQuery(): boolean {
  return useMediaQuery('(min-width: 1024px)'); // Tailwind lg
}

export function useExtraLargeScreenMediaQuery(): boolean {
  return useMediaQuery('(min-width: 1280px)'); // Tailwind xl
}

/**
 * Custom breakpoint hook for any media query
 * Useful for one-off responsive requirements
 */
export function useCustomMediaQuery(query: string): boolean {
  return useMediaQuery(query);
}

/**
 * Orientation-based media queries
 * Useful for tablet/mobile orientation changes
 */
export function usePortraitMediaQuery(): boolean {
  return useMediaQuery('(orientation: portrait)');
}

export function useLandscapeMediaQuery(): boolean {
  return useMediaQuery('(orientation: landscape)');
}

/**
 * Accessibility-aware media queries
 * For users who prefer reduced motion or high contrast
 */
export function usePrefersReducedMotion(): boolean {
  return useMediaQuery('(prefers-reduced-motion: reduce)');
}

export function usePrefersHighContrast(): boolean {
  return useMediaQuery('(prefers-contrast: high)');
}

/**
 * Hover capability detection
 * Useful for touch vs. mouse interactions
 */
export function useHoverCapable(): boolean {
  return useMediaQuery('(hover: hover)');
}

export function usePointerFine(): boolean {
  return useMediaQuery('(pointer: fine)');
}

/**
 * Print media query
 * For print-specific layouts
 */
export function usePrintMediaQuery(): boolean {
  return useMediaQuery('print');
}

/**
 * Dark mode preference detection
 * For automatic theme switching
 */
export function usePrefersDarkMode(): boolean {
  return useMediaQuery('(prefers-color-scheme: dark)');
}
