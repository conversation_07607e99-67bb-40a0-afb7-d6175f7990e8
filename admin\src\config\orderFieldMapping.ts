/**
 * Mapping between frontend field names and backend field names
 * This ensures we send the correct field names in the updatedList
 */
export const ORDER_FIELD_MAPPING: Record<string, string> = {
  // Customer Info
  'user': 'user',
  'phone_number': 'phone_number',
  'email': 'email',
  'shipping_address': 'shipping_address',
  'ward': 'ward',
  'district': 'district',
  'city': 'city',
  
  // Order Items and Pricing
  'items': 'items',
  'shipping_fee': 'shipping_fee',
  'discount': 'discount',
  'tax': 'tax',
  'total_price': 'total_price',
  'final_total': 'final_total',
  'chiet_khau_amount': 'chiet_khau_amount',
  
  // Payment Info
  'payment_method': 'payment_method',
  'payment_status': 'payment_status',
  'company_payment_received': 'company_payment_received',
  
  // Shipping Info
  'shipping_unit': 'shipping_unit',
  'delivery_date': 'delivery_date',
  'delivery_time': 'delivery_time',
  
  // Notes
  'notes': 'notes',
  
  // Staff Assignment
  'sales_admin': 'sales_admin',
  'delivery_staff': 'delivery_staff',
  
  // Status
  'status': 'status',
  'is_showroom': 'is_showroom',
  'showroom_status': 'showroom_status',
  
  // Timestamps
  'confirmation_time': 'confirmation_time',
  'completion_time': 'completion_time',
  'created_at': 'created_at',
  'updated_at': 'updated_at'
};

/**
 * Helper function to map frontend fields to backend fields
 * @param frontendFields Array of frontend field names
 * @returns Array of backend field names
 */
export const mapFieldsToBackend = (frontendFields: string[]): string[] => {
  return frontendFields
    .map(field => ORDER_FIELD_MAPPING[field])
    .filter(Boolean); // Remove undefined mappings
};

/**
 * Get the backend field name for a frontend field
 * @param frontendField Frontend field name
 * @returns Backend field name or undefined if not found
 */
export const getBackendFieldName = (frontendField: string): string | undefined => {
  return ORDER_FIELD_MAPPING[frontendField];
};

/**
 * Check if a field is trackable (has a mapping)
 * @param fieldName Field name to check
 * @returns True if field can be tracked
 */
export const isTrackableField = (fieldName: string): boolean => {
  return fieldName in ORDER_FIELD_MAPPING;
};

/**
 * Fields that should trigger notifications when changed
 * These are important fields that customers/staff should be notified about
 */
export const NOTIFICATION_FIELDS = [
  'status',
  'delivery_date',
  'delivery_time',
  'shipping_unit',
  'sales_admin',
  'delivery_staff',
  'payment_status',
  'shipping_address',
  'phone_number',
  'notes'
];

/**
 * Check if a field should trigger notifications
 * @param fieldName Field name to check
 * @returns True if field should trigger notifications
 */
export const shouldNotifyForField = (fieldName: string): boolean => {
  return NOTIFICATION_FIELDS.includes(fieldName);
};
