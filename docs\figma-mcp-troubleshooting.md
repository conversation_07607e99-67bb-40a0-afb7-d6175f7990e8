# Figma MCP Troubleshooting Guide

## Common Error: `spawn npx ENOENT`

This error occurs when VS Code/Augment cannot find the `npx` command.

### Solution 1: Use cmd wrapper (Current fix)
The configuration has been updated to use:
```json
{
  "command": "cmd",
  "args": ["/c", "npx", "-y", "figma-developer-mcp", "--figma-api-key=YOUR_KEY", "--stdio"]
}
```

### Solution 2: Use full path to npx
If Solution 1 doesn't work, try the alternative configuration:

1. Copy `.vscode/settings-alternative.json` to `.vscode/settings.json`
2. This uses the full path: `C:\\Program Files\\nodejs\\npx.cmd`

### Solution 3: Add Node.js to PATH
1. Open System Properties → Advanced → Environment Variables
2. Add `C:\Program Files\nodejs` to your PATH
3. Restart VS Code completely

### Solution 4: Use PowerShell
```json
{
  "command": "powershell",
  "args": ["-Command", "npx", "-y", "figma-developer-mcp", "--figma-api-key=YOUR_KEY", "--stdio"]
}
```

## Verification Steps

### 1. Test Node.js in Terminal
```bash
node --version
npm --version
npx --version
```

### 2. Test MCP Server Manually
```bash
npx -y figma-developer-mcp --figma-api-key=YOUR_KEY --stdio
```

### 3. Check VS Code Environment
In VS Code terminal, run:
```bash
echo $env:PATH  # PowerShell
echo %PATH%     # Command Prompt
```

## Other Common Issues

### "Invalid API Key"
- Verify your Figma API key is correct
- Check for extra spaces or characters
- Regenerate the key if needed

### "Cannot access Figma file"
- Ensure you have access to the file
- Check if the file URL is correct
- Try with a public Figma community file

### "MCP server timeout"
- Increase timeout in configuration
- Check internet connection
- Try with a smaller Figma file

## Debug Mode

Enable debug logging by adding `--debug` to args:
```json
"args": ["/c", "npx", "-y", "figma-developer-mcp", "--figma-api-key=YOUR_KEY", "--stdio", "--debug"]
```

## Getting Help

If issues persist:
1. Check VS Code Developer Console (Help → Toggle Developer Tools)
2. Look for error messages in Augment logs
3. Try the manual npx command in terminal
4. Report the issue with:
   - VS Code version
   - Node.js version
   - Operating system
   - Full error message
