feat: Add discount column and update total calculation in single order export

- Add "Chiết khấu" column between "Đơn giá" and "Thành tiền" in single order export
- Display chiet_khau_amount from OrderItem model
- Handle null/undefined discount values by showing 0
- Update total row positioning to accommodate new column
- Change total calculation to sum of item.total_price instead of order.total_price
- Only applies to regular orders (not showroom orders)
- Add comprehensive test coverage for discount scenarios
- Update documentation with new column structure

Files changed:
- admin/src/utils/export/singleOrderExportUtils.ts
- admin/src/utils/export/__tests__/singleOrderExportUtils.test.ts
- admin/src/utils/export/README.md
