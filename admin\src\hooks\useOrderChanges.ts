import { useState, useEffect, useCallback } from 'react';
import { Order } from '@/types/order';

interface UseOrderChangesReturn {
  originalOrder: Order | null;
  currentOrder: Order | null;
  changedFields: string[];
  updateField: (fieldName: string, value: any) => void;
  updateFields: (updates: Partial<Order>) => void;
  resetChanges: () => void;
  hasChanges: boolean;
  setOriginalOrder: (order: Order | null) => void;
  setChangedFields: (fields: string[]) => void;
}

export const useOrderChanges = (initialOrder: Order | null): UseOrderChangesReturn => {
  const [originalOrder, setOriginalOrder] = useState<Order | null>(initialOrder);
  const [currentOrder, setCurrentOrder] = useState<Order | null>(initialOrder);
  const [changedFields, setChangedFields] = useState<string[]>([]);

  // Update states when initialOrder changes
  useEffect(() => {
    if (initialOrder) {
      setOriginalOrder(initialOrder);
      setCurrentOrder(initialOrder);
      setChangedFields([]);
    }
  }, [initialOrder]);

  // Deep comparison function
  const isEqual = (a: any, b: any): boolean => {
    if (a === b) return true;
    if (a == null || b == null) return false;
    if (typeof a !== typeof b) return false;
    
    if (typeof a === 'object') {
      if (Array.isArray(a) !== Array.isArray(b)) return false;
      
      if (Array.isArray(a)) {
        if (a.length !== b.length) return false;
        return a.every((item, index) => isEqual(item, b[index]));
      }
      
      const keysA = Object.keys(a);
      const keysB = Object.keys(b);
      if (keysA.length !== keysB.length) return false;
      
      return keysA.every(key => isEqual(a[key], b[key]));
    }
    
    return false;
  };

  // Function to update a specific field
  const updateField = useCallback((fieldName: string, value: any) => {
    setCurrentOrder(prev => {
      if (!prev) return prev;
      const newOrder = { ...prev, [fieldName]: value };
      
      // Check if this field has actually changed from original
      const originalValue = originalOrder?.[fieldName as keyof Order];
      const hasChanged = !isEqual(originalValue, value);
      
      setChangedFields(prevFields => {
        const newFields = [...prevFields];
        const fieldIndex = newFields.indexOf(fieldName);
        
        if (hasChanged && fieldIndex === -1) {
          // Field changed and not in list - add it
          newFields.push(fieldName);
        } else if (!hasChanged && fieldIndex !== -1) {
          // Field reverted to original - remove it
          newFields.splice(fieldIndex, 1);
        }
        
        return newFields;
      });
      
      return newOrder;
    });
  }, [originalOrder]);

  // Function to update multiple fields at once
  const updateFields = useCallback((updates: Partial<Order>) => {
    setCurrentOrder(prev => {
      if (!prev) return prev;
      const newOrder = { ...prev, ...updates };
      
      setChangedFields(prevFields => {
        let newFields = [...prevFields];
        
        Object.entries(updates).forEach(([fieldName, value]) => {
          const originalValue = originalOrder?.[fieldName as keyof Order];
          const hasChanged = !isEqual(originalValue, value);
          const fieldIndex = newFields.indexOf(fieldName);
          
          if (hasChanged && fieldIndex === -1) {
            newFields.push(fieldName);
          } else if (!hasChanged && fieldIndex !== -1) {
            newFields.splice(fieldIndex, 1);
          }
        });
        
        return newFields;
      });
      
      return newOrder;
    });
  }, [originalOrder]);

  // Reset function
  const resetChanges = useCallback(() => {
    setCurrentOrder(originalOrder);
    setChangedFields([]);
  }, [originalOrder]);

  // Check if there are any changes
  const hasChanges = changedFields.length > 0;

  return {
    originalOrder,
    currentOrder,
    changedFields,
    updateField,
    updateFields,
    resetChanges,
    hasChanges,
    setOriginalOrder,
    setChangedFields
  };
};
