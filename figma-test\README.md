# ParkMate Admin Dashboard

A static HTML/CSS/JS implementation of the ParkMate admin dashboard based on the Figma design.

## 🚀 Features

### Dashboard Layout
- **Responsive sidebar navigation** with active states
- **Top navigation bar** with user actions
- **Statistics cards** with interactive close buttons
- **Parking slot grid** with real-time status updates

### Interactive Elements
- **Clickable parking slots** - Toggle between available/occupied
- **Navigation menu** - Switch between different sections
- **Statistics cards** - Show detail modals and close functionality
- **Real-time updates** - Parking status and revenue calculations
- **Toast notifications** - User feedback for actions

### Design Fidelity
- **Exact color scheme** from Figma design
- **Precise typography** using Inter and Roboto fonts
- **Accurate spacing** and layout measurements
- **Hover effects** and smooth transitions
- **Responsive design** for different screen sizes

## 🎨 Design Specifications

### Color Palette
- **Primary Blue**: `#0388B4` (Sidebar background)
- **Secondary Green**: `#2AD95B` (Active nav items, available slots)
- **Background Gray**: `#EFEFEF` (Main background)
- **Occupied Red**: `#FF0B1F` (Occupied parking slots)
- **Available Green**: `#0BFF1F` (Available parking slots)
- **White**: `#FFFFFF` (Cards, navbar)
- **Dark Text**: `#1E1E1E` (Primary text)

### Typography
- **Logo**: Inter 800, 40px
- **Page Title**: Inter 800, 40px
- **Card Titles**: Roboto 400, 45px
- **Card Values**: Inter 600, 24px
- **Navigation**: Inter 500, 25px
- **Parking Status**: Inter 600, 24px

### Layout Dimensions
- **Sidebar Width**: 254px
- **Navbar Height**: 134px
- **Card Width**: 466px
- **Parking Slot**: 275px × 55px
- **Grid Gap**: 19px × 16px

## 📁 File Structure

```
figma-test/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling
├── script.js           # Interactive JavaScript
└── README.md          # Project documentation
```

## 🛠️ Technologies Used

- **HTML5** - Semantic markup structure
- **CSS3** - Modern styling with Grid and Flexbox
- **Vanilla JavaScript** - Interactive functionality
- **Font Awesome** - Icon library
- **Google Fonts** - Inter and Roboto typography

## 🚀 Getting Started

1. **Clone or download** the project files
2. **Open `index.html`** in a modern web browser
3. **Interact with the dashboard**:
   - Click parking slots to toggle status
   - Navigate between sections using sidebar
   - Close statistics cards with X button
   - Click "Show detail" for modal popups

## 💡 Interactive Features

### Parking Slot Management
- **Click any parking slot** to toggle between available (green) and occupied (red)
- **Real-time statistics** update automatically
- **Visual feedback** with hover effects and animations

### Navigation
- **Sidebar navigation** with active state highlighting
- **Page title** updates based on selected section
- **Responsive design** collapses sidebar on mobile

### Statistics Cards
- **Close button** to dismiss cards with fade animation
- **Show detail button** opens modal with additional information
- **Auto-updating values** based on parking slot status

### User Interface
- **Toast notifications** for user actions
- **Smooth transitions** and hover effects
- **Modal dialogs** for detailed views
- **Responsive layout** adapts to screen size

## 📱 Responsive Design

- **Desktop**: Full layout with sidebar and grid
- **Tablet**: Adjusted grid layout and spacing
- **Mobile**: Collapsed sidebar, single-column grid

## 🎯 Figma Design Accuracy

This implementation closely matches the original Figma design:
- ✅ Exact color values and typography
- ✅ Precise spacing and dimensions
- ✅ Component hierarchy and structure
- ✅ Interactive states and behaviors
- ✅ Responsive adaptations

## 🔧 Customization

### Adding New Parking Slots
```javascript
// Modify the parking grid in index.html
// Update CSS grid-template-columns in styles.css
// Adjust JavaScript slot initialization
```

### Changing Colors
```css
/* Update CSS custom properties */
:root {
    --primary-blue: #0388B4;
    --secondary-green: #2AD95B;
    --occupied-red: #FF0B1F;
    --available-green: #0BFF1F;
}
```

### Adding New Navigation Items
```html
<!-- Add to sidebar-nav in index.html -->
<div class="nav-item">
    <i class="fas fa-icon-name"></i>
    <span>New Section</span>
</div>
```

## 📊 Performance

- **Lightweight**: No external frameworks
- **Fast loading**: Optimized CSS and minimal JavaScript
- **Smooth animations**: Hardware-accelerated transitions
- **Responsive**: Efficient layout calculations

## 🌟 Future Enhancements

- **Real-time data integration** with backend API
- **User authentication** and role management
- **Advanced filtering** and search functionality
- **Data visualization** charts and graphs
- **Mobile app** companion
- **Print functionality** for reports

---

**Built with ❤️ following the ParkMate Figma design specifications**
