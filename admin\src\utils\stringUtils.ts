export const removeVietnameseDiacritics = (str: string): string => {
  if (!str) {
    return '';
  }
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/đ/g, 'd')
    .replace(/Đ/g, 'D');
};

export const formatUnitString = (str: string): string => {
  if (!str) {
    return '';
  }
  
  const withoutDiacritics = removeVietnameseDiacritics(str);
  
  return withoutDiacritics.charAt(0).toUpperCase() + withoutDiacritics.slice(1).toLowerCase();
};
