import { Checkbox, Input, Form } from "antd";

interface TaxSectionProps {
  taxEnabled: boolean;
  onTaxEnabledChange: (enabled: boolean) => void;
  taxValue: number;
  onTaxValueChange: (value: number) => void;
}

export function TaxSection({
  taxEnabled,
  onTaxEnabledChange,
  taxValue,
  onTaxValueChange,
}: TaxSectionProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow space-y-4">
      <h2 className="text-xl font-semibold mb-4">Thuế</h2>
      <Form.Item className="mb-0">
        <Checkbox
          checked={taxEnabled}
          onChange={(e) => onTaxEnabledChange(e.target.checked)}
        >
          Áp dụng thuế
        </Checkbox>
      </Form.Item>
      <Form.Item label="Giá trị thuế (%)">
        <Input
          type="number"
          value={taxValue}
          onChange={(e) => onTaxValueChange(Number(e.target.value))}
          disabled={!taxEnabled}
          className="w-full"
          min={0}
        />
      </Form.Item>
    </div>
  );
}
