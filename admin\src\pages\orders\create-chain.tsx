import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, Button, message } from "antd";

import { api, endpoints } from "@/lib/api";
import { useAuth } from "@/context/auth-hooks";
import { useToast } from "@/context/toast-hooks";

import { isApiError } from "@/types/api";
import ChainProductTable from "@/components/orders/ChainProductTable";
import { ShippingAddressForm, type ChainOrderForm } from "./components";



interface ChainOrderItem {
  id: number;
  name: string;
  code?: string;
  price: number;
  quantity: number;
  total_price: number;
  unit?: string;
  weight?: number;
}


export default function CreateChainOrderPage() {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { user } = useAuth();

  const [chainProducts, setChainProducts] = useState<ChainOrderItem[]>([]);
  const [loading, setLoading] = useState(false);

  const [orderForm, setOrderForm] = useState<ChainOrderForm>({
    user: user?.id,
    phone_number: user?.phone_number || "",
    email: user?.email || "",
    shipping_address: "",
    ward: "",
    district: "",
    city: "",
    payment_method: "cash" as const, // Mặc định là Tiền mặt
    payment_status: "unpaid" as const, // Mặc định là chưa thanh toán
    company_payment_received: false,
    shipping_fee: 0,
    discount: 0,
    is_chain: true,
    tax: 0,
    have_tax: false,
  });





  const handleChainProductsChange = (products: any[]) => {
    const formattedProducts: ChainOrderItem[] = products.map(product => ({
      id: product.id,
      name: product.name,
      code: product.code,
      price: product.chain_price || 0,
      quantity: product.quantity,
      total_price: product.total_price,
      unit: product.unit,
      weight: product.weight,
    }));
    setChainProducts(formattedProducts);
  };

  const handleOrderFormChange = (field: string, value: string) => {
    setOrderForm(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Validate required fields
      if (chainProducts.length === 0) {
        message.error("Vui lòng chọn ít nhất một sản phẩm");
        return;
      }

      if (!user) {
        message.error("Không thể xác định người dùng");
        return;
      }

      if (!orderForm.shipping_address.trim()) {
        message.error("Vui lòng nhập địa chỉ giao hàng");
        return;
      }

      if (!orderForm.city || !orderForm.district || !orderForm.ward) {
        message.error("Vui lòng chọn đầy đủ thông tin thành phố, quận/huyện, phường/xã");
        return;
      }

      // Prepare order data
      const orderData = {
        ...orderForm,
        sales_admin: user.id, // Gán sales_admin là user đang đăng nhập
        items: chainProducts.map(product => ({
          product: product.id,
          quantity: product.quantity,
          price: product.price, // Đây là price đã được format từ chain_price ở trên
          total_price: product.total_price,
        })),
      };

      // Create order
      const response = await api.post(endpoints.orders.create, orderData);

      showToast("Đơn hàng chuỗi đã được tạo thành công!", "success");
      navigate(`/orders/${response.data.id}`);
    } catch (error) {
      console.error("Lỗi khi tạo đơn hàng:", error);

      if (isApiError(error)) {
        const errorMessage = typeof error.response?.data === 'string'
          ? error.response.data
          : "Có lỗi xảy ra khi tạo đơn hàng";
        message.error(errorMessage);
      } else {
        message.error("Có lỗi xảy ra khi tạo đơn hàng");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Tạo đơn hàng chuỗi</h1>
        <Button onClick={() => navigate("/orders/chain")}>
          Quay lại danh sách
        </Button>
      </div>

      {/* Chain Products Table */}
      <Card title="Sản phẩm chuỗi" className="mt-6">
        <ChainProductTable onProductsChange={handleChainProductsChange} />
      </Card>

      {/* Shipping Address Form */}
      <ShippingAddressForm
        orderForm={orderForm}
        onOrderFormChange={handleOrderFormChange}
      />

      {/* Create Order Button */}
      <div className="mt-8 flex justify-end">
        <Button
          type="primary"
          size="large"
          onClick={handleSubmit}
          loading={loading}
          disabled={
            loading ||
            chainProducts.length === 0
          }
          className="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
        >
          {loading ? "Đang tạo..." : "Tạo đơn hàng chuỗi"}
        </Button>
      </div>


    </div>
  );
}
