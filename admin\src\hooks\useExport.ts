import { useState, useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiCall, endpoints } from '@/lib/api';
import { Order } from '@/types/order';
import { Staff } from '@/types/staff';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DEFAULT_EXPORT_FIELDS,
  EXPORT_TOAST_MESSAGES,
  ITEMS_PER_PAGE,
  EXCEL_EXPORT,
} from '@/constants/constants';
import {
  createMultiOrderWorkbook,
} from '@/utils/export/multiOrderExportHelpers';
import { 
  NEW_EXPORT_COLUMNS, 
  generateNewMultiOrderExportData 
} from '@/utils/export/multiOrderExportUtils';

interface UseExportProps {
  user: any;
  showToast: (message: string, type: 'success' | 'error') => void;
}

export const useExport = ({ user, showToast }: UseExportProps) => {
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<[string | null, string | null]>([null, null]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectAllFilteredActive, setSelectAllFilteredActive] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = ITEMS_PER_PAGE;
  const [isExportingMulti, setIsExportingMulti] = useState(false);
  
  // Filter states
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<string[]>([]);
  const [orderTypeFilters, setOrderTypeFilters] = useState<string[]>([]);
  
  // Staff filter states
  const [selectedSalesAdmin, setSelectedSalesAdmin] = useState<Staff | null>(null);
  const [selectedSalesAdmins, setSelectedSalesAdmins] = useState<number[]>([]);
  
  const [selectedExportFields, setSelectedExportFields] = useState<string[]>(DEFAULT_EXPORT_FIELDS);
  
  useEffect(() => {
    if (selectedExportFields.length === 0 && DEFAULT_EXPORT_FIELDS.length > 0) {
       setSelectedExportFields(DEFAULT_EXPORT_FIELDS);
    }
  }, [selectedExportFields]);
  
  // Data fetching
  const { data: staffList = [] } = useQuery<Staff[]>({
    queryKey: ["staff", { no_page: true }],
    queryFn: async () => {
      const response = await apiCall<{ results: Staff[] }>(
        "GET",
        endpoints.staff.list + "?no_page=true"
      );
      return response.results;
    },
  });
  
  const { data: ordersData, isLoading, error } = useQuery<{ results: Order[]; count: number }>({
    queryKey: [
      "orders",
      "export", 
      {
        page: currentPage,
        pageSize: pageSize,
        search: searchTerm || undefined,
        dateRange: dateRange[0] || dateRange[1] ? { from: dateRange[0], to: dateRange[1] } : undefined,
        filters: {
          status: statusFilter.length > 0 ? statusFilter : undefined,
          paymentStatus: paymentStatusFilter.length > 0 ? paymentStatusFilter : undefined,
          order_type: orderTypeFilters.length > 0 ? orderTypeFilters.join(',') : undefined,
          salesAdmins: selectedSalesAdmins.length > 0 ? selectedSalesAdmins : undefined,
        },
        user: user ? { id: user.id, role: user.role } : undefined,
      }
    ],
    queryFn: async () => {
      let url = `${endpoints.orders.list}?page=${currentPage}&page_size=${pageSize}`;
      if (searchTerm) {
        url += `&search=${encodeURIComponent(searchTerm)}&search_by=customer_name_or_id`;
      }

      if (dateRange[0]) {
        url += `&date_from=${dateRange[0]}`;
      }
      if (dateRange[1]) {
        url += `&date_to=${dateRange[1]}`;
      }      
      if (statusFilter.length > 0) {
        url += `&status=${statusFilter.join(',')}`;
      }
      if (paymentStatusFilter.length > 0) {
        url += `&payment_status=${paymentStatusFilter.join(',')}`;
      }      
      if (orderTypeFilters.length > 0) {
        url += `&order_type=${orderTypeFilters.join(',')}`;
      }

      if (selectedSalesAdmins.length > 0) {
        url += `&sales_admin=${selectedSalesAdmins.join(',')}`;
      }

      if (user?.role === "sales_admin" && user.id) {
         url = `${endpoints.orders.list}/by_sales_admin/?id=${user.id}&page=${currentPage}&page_size=${pageSize}`;
         if (searchTerm) {
           url += `&search=${encodeURIComponent(searchTerm)}&search_by=customer_name_or_id`;
         }
         
         if (dateRange[0]) {
           url += `&date_from=${dateRange[0]}`;
         }
         if (dateRange[1]) {
           url += `&date_to=${dateRange[1]}`;
         }         
         
         if (statusFilter.length > 0) {
           url += `&status=${statusFilter.join(',')}`;
         }
         if (paymentStatusFilter.length > 0) {
           url += `&payment_status=${paymentStatusFilter.join(',')}`;
         }
         if (orderTypeFilters.length > 0) {
           url += `&order_type=${orderTypeFilters.join(',')}`;
         }
      }
      return apiCall('GET', url);
    },
    enabled: !!user,
  });

  const orders = useMemo(() => ordersData?.results || [], [ordersData]);
  const totalOrders = useMemo(() => ordersData?.count || 0, [ordersData]);

  // Function to fetch all orders matching the current filters
  const fetchAllFilteredOrders = async (): Promise<Order[]> => {
    if (!user) return [];

    let allMatchingOrders: Order[] = [];
    let currentPageToFetch = 1;
    const fetchPageSize = 100;
    let hasMore = true;
    let totalFetchedCount = 0;

    // Construct base URL similar to useQuery
    let baseUrl = `${endpoints.orders.list}?`;
    if (user.role === "sales_admin" && user.id) {
      baseUrl = `${endpoints.orders.list}/by_sales_admin/?id=${user.id}&`;
    }
    if (searchTerm) {
      baseUrl += `search=${encodeURIComponent(searchTerm)}&search_by=customer_name_or_id&`;
    }    
    // Add date range parameters
    if (dateRange[0]) {
      baseUrl += `date_from=${dateRange[0]}&`;
    }
    if (dateRange[1]) {
      baseUrl += `date_to=${dateRange[1]}&`;
    }
    // Add status filters - support multiple values
    if (statusFilter.length > 0) {
      baseUrl += `status=${statusFilter.join(',')}&`;
    }
    if (paymentStatusFilter.length > 0) {
      baseUrl += `payment_status=${paymentStatusFilter.join(',')}&`;
    }    
    if (orderTypeFilters.length > 0) {
      baseUrl += `order_type=${orderTypeFilters.join(',')}&`;
    }

    if (selectedSalesAdmins.length > 0) {
      baseUrl += `sales_admin=${selectedSalesAdmins.join(',')}&`;
    }

    showToast(EXPORT_TOAST_MESSAGES.LOADING_ALL_ORDERS, "success");

    while (hasMore) {
      const url = `${baseUrl}page=${currentPageToFetch}&page_size=${fetchPageSize}`;
      try {
        const response = await apiCall<{ results: Order[]; count?: number }>('GET', url);
        if (response.results && response.results.length > 0) {
          allMatchingOrders = allMatchingOrders.concat(response.results);
          totalFetchedCount += response.results.length;
        }
        if (response.results && response.results.length > 0) {
          currentPageToFetch++;
        } else {
          hasMore = false;
        }
      } catch (fetchError: any) {
        if (fetchError?.response?.status === 404) {
          hasMore = false;
        } else {
          console.error("Error fetching all filtered orders:", fetchError);
          showToast(EXPORT_TOAST_MESSAGES.FETCH_ALL_ORDERS_ERROR, "error");
          hasMore = false;
          return [];
        }
      }
    }
    if (allMatchingOrders.length > 0) {
        showToast(EXPORT_TOAST_MESSAGES.ORDERS_LOADED(allMatchingOrders.length), "success");
    } else {
        showToast(EXPORT_TOAST_MESSAGES.NO_ORDERS_FOUND_FILTER, "success");
    }
    return allMatchingOrders;
  };

  // Event handlers
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleDateRangeChange = (dates: [string | null | undefined, string | null | undefined]) => {
    setDateRange([
      dates[0] || null,
      dates[1] || null
    ]);
    setCurrentPage(1);
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectAllFilteredActive(false);
  };

  const rowSelection = {
    selectedRowKeys: selectAllFilteredActive ? orders.map(o => o.id) : selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleToggleSelectAllFiltered = () => {
    if (selectAllFilteredActive) {
      setSelectedRowKeys([]);
      setSelectAllFilteredActive(false);
    } else {
      setSelectedRowKeys(orders.map(order => order.id));
      setSelectAllFilteredActive(true);
    }
  };

  const getOrdersToExport = async (): Promise<Order[] | null> => {
    if (selectedExportFields.length === 0) {
      showToast(EXPORT_TOAST_MESSAGES.SELECT_AT_LEAST_ONE_FIELD, "error");
      return null;
    }

    let ordersToExportInTable: Order[] = [];
    if (selectAllFilteredActive) {
      ordersToExportInTable = await fetchAllFilteredOrders();
    } else {
      ordersToExportInTable = orders.filter(order => selectedRowKeys.includes(order.id));
    }

    if (ordersToExportInTable.length === 0) {
      showToast(EXPORT_TOAST_MESSAGES.NO_ORDERS_SELECTED_FOR_EXPORT, "error");
      return null;
    }

    return ordersToExportInTable;
  };

  const generateAndDownloadExcel = async (workbook: Workbook): Promise<void> => {
    try {
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: EXCEL_EXPORT.FILE_TYPE });
      const fileName = `${EXCEL_EXPORT.MULTI_ORDER_FILE_NAME_PREFIX}${new Date().toISOString().slice(0,10)}.xlsx`;
      saveAs(blob, fileName);
      showToast(EXPORT_TOAST_MESSAGES.EXPORT_SUCCESS, "success");
    } catch (err) {
      console.error('Lỗi khi tạo file XLSX cho nhiều đơn hàng:', err);
      showToast(EXPORT_TOAST_MESSAGES.EXPORT_ERROR, "error");
      throw err;
    }
  };

  // Filter handlers
  const handleStatusFilterChange = (status: string[]) => {
    setStatusFilter(status);
    setCurrentPage(1);
    setSelectedRowKeys([]);
    setSelectAllFilteredActive(false);
  };

  const handlePaymentStatusFilterChange = (paymentStatus: string[]) => {
    setPaymentStatusFilter(paymentStatus);
    setCurrentPage(1);
    setSelectedRowKeys([]);
    setSelectAllFilteredActive(false);
  };

  const handleOrderTypeFilterChange = (values: string[]) => {
    setOrderTypeFilters(values);
    setCurrentPage(1);
    setSelectedRowKeys([]);
    setSelectAllFilteredActive(false);
  };

  const handleSalesAdminChange = (staffId: number | string | null) => {
    if (staffId === "null") {
      setSelectedSalesAdmin({ id: "null", first_name: "Khách hàng", last_name: "tự đặt" } as any);
    } else if (typeof staffId === "number") {
      const staff = staffList.find(s => s.id === staffId) || null;
      setSelectedSalesAdmin(staff);
    } else {
      setSelectedSalesAdmin(null);
    }
    setCurrentPage(1);
    setSelectedRowKeys([]);
    setSelectAllFilteredActive(false);
  };

  const handleSalesAdminsChange = (staffIds: (number | string)[]) => {
    const processedIds: number[] = staffIds
      .filter(id => id !== "null")
      .map(id => typeof id === "string" ? parseInt(id, 10) : id)
      .filter(id => !isNaN(id));

    const hasNullSelection = staffIds.includes("null");

    if (hasNullSelection) {
      setSelectedSalesAdmins(processedIds);
    } else {
      setSelectedSalesAdmins(processedIds);
    }

    setCurrentPage(1);
    setSelectedRowKeys([]);
    setSelectAllFilteredActive(false);
  };

  const handleProceedWithMultiOrderExport = async () => {
    setIsExportingMulti(true);
    showToast(EXPORT_TOAST_MESSAGES.PREPARING_EXCEL, "success");

    try {
      const ordersToExportInTable = await getOrdersToExport();
      if (!ordersToExportInTable) {
        setIsExportingMulti(false);
        return;
      }

      const { workbook, worksheet } = createMultiOrderWorkbook();
      worksheet.addRow(NEW_EXPORT_COLUMNS);
      worksheet.getRow(worksheet.lastRow!.number).font = { bold: true };

      const dataRows = generateNewMultiOrderExportData(ordersToExportInTable);
      dataRows.forEach((row) => {
        const rowValues = NEW_EXPORT_COLUMNS.map(colName => {
          const value = row[colName];
          const finalValue = (value === null || value === undefined) ? '' : value;
          return finalValue;
        });
        worksheet.addRow(rowValues);
      });

      NEW_EXPORT_COLUMNS.forEach((_, i) => {
        const column = worksheet.getColumn(i + 1);
        let maxLength = 0;
        column.eachCell!({ includeEmpty: true }, cell => {
          const columnLength = cell.value ? cell.value.toString().length : 10;
          if (columnLength > maxLength) {
            maxLength = columnLength;
          }
        });
        column.width = maxLength < 12 ? 12 : Math.min(maxLength + 2, 50);
      });

      await generateAndDownloadExcel(workbook);
    } catch (err) {
      console.error('Lỗi khi xuất file Excel:', err);
      showToast(EXPORT_TOAST_MESSAGES.EXPORT_ERROR, "error");
    } finally {
      setIsExportingMulti(false);
    }
  };

  const handleExportSelected = async () => {
    if (selectAllFilteredActive && totalOrders === 0) {
      showToast(EXPORT_TOAST_MESSAGES.NO_ORDERS_MATCH_FILTER_TO_EXPORT, "error");
      return;
    }
    if (!selectAllFilteredActive && selectedRowKeys.length === 0) {
      showToast(EXPORT_TOAST_MESSAGES.SELECT_AT_LEAST_ONE_ORDER, "error");
      return;
    }
    // Always use multi-order export regardless of number of selected orders
    handleProceedWithMultiOrderExport();
  };

  return {
    // States
    searchTerm,
    dateRange,
    selectedRowKeys,
    selectAllFilteredActive,
    currentPage,
    pageSize,
    isExportingMulti,
    statusFilter,
    paymentStatusFilter,
    orderTypeFilters,
    selectedSalesAdmin,
    selectedSalesAdmins,
    selectedExportFields,

    // Data
    orders,
    totalOrders,
    staffList,
    isLoading,
    error,

    // Setters
    setSearchTerm,
    setDateRange,
    setSelectedRowKeys,
    setSelectAllFilteredActive,
    setCurrentPage,
    setStatusFilter,
    setPaymentStatusFilter,
    setOrderTypeFilters,
    setSelectedSalesAdmin,
    setSelectedSalesAdmins,
    setIsExportingMulti,

    // Event handlers
    handleSearch,
    handleDateRangeChange,
    handleToggleSelectAllFiltered,
    handleStatusFilterChange,
    handlePaymentStatusFilterChange,
    handleOrderTypeFilterChange,
    handleSalesAdminChange,
    handleSalesAdminsChange,

    // Row selection
    rowSelection,

    // Export functions
    handleProceedWithMultiOrderExport,
    handleExportSelected,

    // Utility functions
    fetchAllFilteredOrders,
    getOrdersToExport,
    generateAndDownloadExcel,
  };
};
