# 🏥 Healthcheck.io Setup Guide for Scraping Monitor

## 📋 Overview
Theo feedback của tech lead, chúng ta sẽ setup healthcheck.io để monitor chức năng scraping và báo lỗi khi không hoạt động.

## 🚀 Step 1: Tạo tài khoản Healthchecks.io

1. **Truy cập**: https://healthchecks.io/
2. **Đăng ký** tài khoản miễn phí (có thể dùng GitHub/Google)
3. **Verify email** nếu cần

## ⚙️ Step 2: Tạo Check mới

1. **Click "Add Check"** trên dashboard
2. **Cấu hình check**:
   ```
   Name: Scraping Inventory 3T
   Period: 2 hours (hoặc 1 hour tùy theo cron schedule)
   Grace: 10 minutes
   Description: Monitor inventory scraping from Arito
   ```

3. **Advanced settings** (optional):
   - **Timezone**: Asia/Ho_Chi_Minh
   - **Filter keywords**: có thể để trống
   - **Failure keywords**: "error", "failed", "exception"

## 🔗 Step 3: L<PERSON>y Ping URL

Sau khi tạo check, bạn sẽ thấy:
- **Ping URL**: `https://hc-ping.com/your-unique-uuid-here`
- **Start URL**: `https://hc-ping.com/your-unique-uuid-here/start`
- **Fail URL**: `https://hc-ping.com/your-unique-uuid-here/fail`

## 📝 Step 4: Cập nhật .env file

Thay thế UUID trong file `.env`:

```env
# Healthchecks.io monitoring
HEALTHCHECK_URL=https://hc-ping.com/your-actual-uuid-here
```

**Ví dụ**:
```env
HEALTHCHECK_URL=https://hc-ping.com/12345678-1234-1234-1234-123456789abc
```

## 🔔 Step 5: Setup Notifications

### Email Notifications (Recommended)
1. **Trong Healthchecks.io dashboard**:
   - Click vào check vừa tạo
   - Tab "Integrations"
   - Click "Add Integration" → "Email"
   - Nhập email của bạn

### Slack/Telegram (Optional)
- Có thể setup thêm Slack hoặc Telegram nếu cần

## 🧪 Step 6: Test Setup

### Test Manual
```bash
# Test success ping
curl https://hc-ping.com/your-uuid-here

# Test start ping
curl https://hc-ping.com/your-uuid-here/start

# Test failure ping
curl -X POST https://hc-ping.com/your-uuid-here/fail -d "Test failure message"
```

### Test qua API
```bash
# Trigger scraping
curl http://api.nguyenlieuphache3t.vn/api/scrape_inventory
```

## 📊 Step 7: Setup Cron với Crontap.com

1. **Truy cập**: https://crontap.com/
2. **Tạo cron job**:
   ```
   URL: http://api.nguyenlieuphache3t.vn/api/scrape_inventory
   Schedule: Every 2 hours (0 */2 * * *)
   Method: GET
   Timeout: 30 seconds
   ```

## 🔍 Monitoring Dashboard

### Healthchecks.io Dashboard
- **Green**: Scraping hoạt động bình thường
- **Red**: Scraping bị lỗi hoặc không chạy
- **Gray**: Chưa có ping nào

### Log Messages
- **Start ping**: "Scraping started"
- **Success ping**: "Scraping completed successfully"
- **Fail ping**: Error message chi tiết

## 🚨 Alert Scenarios

### Khi nào sẽ báo lỗi:
1. **Không có ping trong 2h + 10min grace period**
2. **Nhận được fail ping với error message**
3. **Start ping nhưng không có success ping trong thời gian grace**

### Notification channels:
1. **Email**: Chi tiết lỗi và thời gian
2. **Healthchecks.io dashboard**: Status update

## 📋 Checklist Setup

- [ ] Tạo tài khoản Healthchecks.io
- [ ] Tạo check "Scraping Inventory 3T"
- [ ] Copy ping URL vào .env file
- [ ] Setup email integration
- [ ] Test manual ping
- [ ] Test API endpoint
- [ ] Setup cron job trên Crontap.com
- [ ] Verify notifications hoạt động

## 🔧 Troubleshooting

### Không nhận được notifications:
1. Verify email integration setup trong Healthchecks.io
2. Check spam folder
3. Test manual ping

### API timeout:
- API sẽ return ngay lập tức với "task started"
- Scraping chạy trong background thread
- Healthcheck ping sẽ được gửi khi hoàn thành

### Healthcheck URL không hoạt động:
1. Verify UUID trong .env file
2. Check network connectivity
3. Xem logs trong Django để debug

## 📞 Support

- **Healthchecks.io docs**: https://healthchecks.io/docs/
- **Discord webhook guide**: https://support.discord.com/hc/en-us/articles/228383668
- **Crontap.com docs**: https://crontap.com/docs/
