<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-TPK7MXZF');</script>
    <!-- End Google Tag Manager -->

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ParkMate - Admin Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1 class="logo">ParkMate</h1>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-item active">
                    <i class="fas fa-th-large"></i>
                    <span>Dashboard</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Report</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>Parking lot</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-car"></i>
                    <span>Vehicle</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-route"></i>
                    <span>Route</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Navbar -->
            <header class="top-navbar">
                <div class="navbar-content">
                    <h2 class="page-title">Available Parking lot</h2>
                    <div class="navbar-actions">
                        <button class="icon-btn">
                            <i class="fas fa-bell"></i>
                        </button>
                        <button class="icon-btn">
                            <i class="fas fa-user"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <!-- Statistics Cards -->
                <div class="stats-section">
                    <div class="stat-card">
                        <button class="close-btn">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="card-content">
                            <h3 class="card-title">Current vehicles</h3>
                            <p class="card-value">12</p>
                            <button class="detail-btn">Show detail</button>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <button class="close-btn">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="card-content">
                            <h3 class="card-title">Revenue</h3>
                            <p class="card-value">20.000 VND</p>
                            <button class="detail-btn">Show detail</button>
                        </div>
                    </div>
                </div>

                <!-- Parking Slots Grid -->
                <div class="parking-section">
                    <div class="parking-header">
                        <h3 class="parking-title">Parking status: Available</h3>
                    </div>
                    
                    <div class="parking-grid">
                        <div class="parking-slot occupied">
                            <span class="slot-number">1</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">2</span>
                        </div>
                        <div class="parking-slot occupied">
                            <span class="slot-number">3</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">4</span>
                        </div>
                        
                        <div class="parking-slot occupied">
                            <span class="slot-number">5</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">6</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">7</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">8</span>
                        </div>
                        
                        <div class="parking-slot available">
                            <span class="slot-number">9</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">10</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">11</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">12</span>
                        </div>
                        
                        <div class="parking-slot available">
                            <span class="slot-number">13</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">14</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">15</span>
                        </div>
                        <div class="parking-slot available">
                            <span class="slot-number">16</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
