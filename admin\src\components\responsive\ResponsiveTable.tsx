import React, { ReactNode } from "react";
import { Table, Button, Space, Pagination } from "antd";
import { ColumnsType, ColumnType } from "antd/es/table";
import { useResponsive } from "@/hooks/useResponsive";

interface ResponsiveTableProps<T extends Record<string, any> = any> {
  columns: ColumnsType<T>;
  dataSource: T[];
  loading?: boolean;
  rowKey?: string | ((record: T) => string);
  pagination?: any;
  scroll?: { x?: number; y?: number };
  size?: "small" | "middle" | "large";
  className?: string;
  onRow?: (record: T, index?: number) => any;
  rowSelection?: any;
  rowClassName?: (record: T, index?: number) => string;
  bordered?: boolean;
  // Mobile card props
  mobileCardRender?: (record: T, index: number) => ReactNode;
  mobileCardTitle?: (record: T) => ReactNode;
  mobileCardActions?: (record: T) => ReactNode[];
  mobileGridCols?: 1 | 2 | 3;
}

// Type guard to check if column is a ColumnType (not ColumnGroupType)
const isColumnType = <T,>(column: ColumnsType<T>[0]): column is ColumnType<T> => {
  return 'dataIndex' in column;
};

export function ResponsiveTable<T extends Record<string, any> = any>({
  columns,
  dataSource,
  loading = false,
  rowKey = "id",
  pagination,
  scroll,
  size = "middle",
  className = "",
  onRow,
  rowSelection,
  rowClassName,
  bordered = false,
  mobileCardRender,
  mobileCardTitle,
  mobileCardActions,
  mobileGridCols = 1,
}: ResponsiveTableProps<T>) {
  const { isMobile, isTablet } = useResponsive();

  // Mobile/Tablet Card View
  if (isMobile || isTablet) {
    const renderMobileCard = (record: T, index: number) => {
      // Custom card render if provided
      if (mobileCardRender) {
        return mobileCardRender(record, index);
      }

      // Default card render based on columns
      const title = mobileCardTitle 
        ? mobileCardTitle(record)
        : `Item ${index + 1}`;

      const actions = mobileCardActions 
        ? mobileCardActions(record)
        : [];

      const getRecordKey = (record: T): string => {
        if (typeof rowKey === "string") {
          return String(record[rowKey] || index);
        }
        return rowKey(record);
      };

      return (
        <div key={getRecordKey(record)} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3 transition-shadow duration-200 hover:shadow-md">
          <div className="font-semibold text-base mb-3 text-gray-900 border-b border-gray-100 pb-2">
            {title}
          </div>
          
          <div className="space-y-2">
            {columns.map((column, colIndex) => {
              // Skip if not a column type or no dataIndex
              if (!isColumnType(column) || !column.dataIndex) return null;
              
              // Skip if this is the row key column
              if (column.dataIndex === rowKey) return null;
              
              const dataIndex = column.dataIndex as keyof T;
              const value = record[dataIndex];
              
              let displayValue: ReactNode = value;
              if (column.render) {
                const rendered = column.render(value, record, index);
                // Handle RenderedCell type which can be an object with props and children
                if (rendered && typeof rendered === 'object' && 'props' in rendered) {
                  displayValue = rendered.props?.children || String(value);
                } else {
                  displayValue = rendered as ReactNode;
                }
              } else if (value !== null && value !== undefined) {
                displayValue = String(value);
              }

              return (
                <div key={colIndex} className="flex justify-between items-center py-1 min-h-[2rem]">
                  <div className="font-medium text-gray-600 text-sm flex-1">
                    {String(column.title || '')}
                  </div>
                  <div className="text-gray-900 text-right flex-1 break-words">
                    {displayValue}
                  </div>
                </div>
              );
            })}
          </div>

          {actions.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-100 flex gap-2 flex-wrap">
              {actions.map((action, actionIndex) => (
                <React.Fragment key={actionIndex}>
                  {action}
                </React.Fragment>
              ))}
            </div>
          )}
        </div>
      );
    };

    const gridClass = isTablet && mobileGridCols > 1 
      ? `grid grid-cols-${mobileGridCols} gap-4`
      : "";

    return (
      <div className={`w-full overflow-hidden rounded-lg border border-gray-200 bg-white ${className}`}>
        {loading && (
          <div className="flex justify-center items-center min-h-[200px] p-8">
            <div>Loading...</div>
          </div>
        )}
        
        {!loading && dataSource.length === 0 && (
          <div className="text-center p-8 text-red-600">
            <div className="mb-4 text-base">
              No data available
            </div>
          </div>
        )}

        {!loading && dataSource.length > 0 && (
          <div className={`p-3 ${gridClass}`}>
            {dataSource.map((record, index) => renderMobileCard(record, index))}
          </div>
        )}

        {pagination && (
          <div className="p-4 flex justify-center">
            <Pagination {...pagination} />
          </div>
        )}
      </div>
    );
  }
  // Desktop Table View
  return (
    <div className={`w-full overflow-hidden rounded-lg border border-gray-200 bg-white ${className}`}>
      <div className="block overflow-x-auto">        
        <Table<T>
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          rowKey={rowKey}
          pagination={pagination}
          scroll={scroll}
          size={size}
          onRow={onRow}
          rowSelection={rowSelection}
          rowClassName={rowClassName}
          bordered={bordered}
        />
      </div>
    </div>
  );
}

// Helper function to create mobile-friendly action buttons
export const createMobileAction = (
  label: string,
  onClick: () => void,
  type: "primary" | "default" | "dashed" | "link" | "text" = "default",
  danger: boolean = false,
  icon?: ReactNode
) => (
  <Button
    type={type}
    danger={danger}
    onClick={onClick}
    icon={icon}
    className="touch-button"
    size="small"
  >
    {label}
  </Button>
);

// Helper function to create responsive columns that work well on mobile
export const createResponsiveColumn = <T,>(
  title: string,
  dataIndex: keyof T,
  render?: (value: any, record: T, index: number) => ReactNode,
  width?: number,
  fixed?: "left" | "right",
  sorter?: boolean | ((a: T, b: T) => number),
  filters?: Array<{ text: string; value: any }>,
  onFilter?: (value: any, record: T) => boolean
): ColumnsType<T>[0] => ({
  title,
  dataIndex: dataIndex as string,
  key: dataIndex as string,
  render,
  width,
  fixed,
  sorter,
  filters,
  onFilter,
  ellipsis: true, // Enable ellipsis for long content
});

// Helper function to create action column that works on all devices
export const createActionColumn = <T,>(
  actions: (record: T, index: number) => ReactNode[]
): ColumnsType<T>[0] => ({
  title: "Actions",
  key: "actions",
  fixed: "right",
  width: 120,
  render: (_, record, index) => (
    <Space size="small" wrap>
      {actions(record, index).map((action, actionIndex) => (
        <React.Fragment key={actionIndex}>
          {action}
        </React.Fragment>
      ))}
    </Space>
  ),
});
