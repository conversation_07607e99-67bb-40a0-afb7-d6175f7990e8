import { Card, Typography, Empty, Button, InputNumber } from "antd";
import { DeleteOutlined, ShoppingCartOutlined } from "@ant-design/icons";
import { type ChainProductItem } from "@/hooks/orders";

const { Text } = Typography;

interface SelectedChainProductsProps {
  selectedProducts: ChainProductItem[];
  onRemoveProduct?: (productId: number) => void;
  onQuantityChange?: (productId: number, quantity: number) => void;
  onClearAll?: () => void;
  className?: string;
}

export default function SelectedChainProducts({
  selectedProducts,
  onRemoveProduct,
  onQuantityChange,
  onClearAll,
  className = ""
}: SelectedChainProductsProps) {
  const totalQuantity = selectedProducts.reduce((sum, product) => sum + product.quantity, 0);

  if (selectedProducts.length === 0) {
    return (
      <Card 
        title={
          <div className="flex items-center gap-2">
            <ShoppingCartOutlined className="text-blue-500" />
            <span><PERSON>ản phẩm đã chọn</span>
          </div>
        }
        className={`${className}`}
      >
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="Chưa có sản phẩm nào được chọn"
        />
      </Card>
    );
  }

  return (
    <Card 
      title={
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ShoppingCartOutlined className="text-blue-500" />
            <span>Sản phẩm đã chọn</span>
          </div>
          {onClearAll && (
            <Button 
              size="small" 
              danger 
              icon={<DeleteOutlined />}
              onClick={onClearAll}
            >
              Xóa tất cả
            </Button>
          )}
        </div>
      }
      className={`${className}`}
    >
      <div className="space-y-4">

        {/* Products List */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {selectedProducts.map((product) => (
            <div
              key={product.id}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-start gap-3">
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-900 truncate">
                      {product.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {product.code || "Chưa có mã"}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3 ml-4">
                <div className="flex items-center gap-2">
                  <Text className="text-sm text-gray-500">Số lượng:</Text>
                  <InputNumber
                    min={1}
                    value={product.quantity}
                    onChange={(value) => onQuantityChange?.(product.id, value || 1)}
                    className="w-20"
                    size="small"
                  />
                </div>

                {onRemoveProduct && (
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => onRemoveProduct(product.id)}
                    className="flex-shrink-0"
                  />
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Footer Summary */}
        <div className="border-t pt-4">
          <div className="text-center">
            <Text type="secondary">
              Tổng cộng: {totalQuantity.toLocaleString()}
            </Text>
          </div>
        </div>
      </div>
    </Card>
  );
}
