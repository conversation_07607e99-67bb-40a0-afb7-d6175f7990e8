/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #EFEFEF;
    height: 100vh;
    overflow: hidden;
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* Sidebar Styles */
.sidebar {
    width: 254px;
    background-color: #0388B4;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
}

.sidebar-header {
    padding: 46px 0;
    text-align: center;
}

.logo {
    font-family: 'Inter', sans-serif;
    font-weight: 800;
    font-size: 40px;
    line-height: 1.21;
    color: #FFFFFF;
    margin: 0;
}

.sidebar-nav {
    padding: 0 21px;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 9px 0;
    margin-bottom: 122px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.nav-item:first-child {
    margin-bottom: 122px;
}

.nav-item:nth-child(2) {
    margin-bottom: 122px;
}

.nav-item:nth-child(3) {
    margin-bottom: 122px;
}

.nav-item:nth-child(4) {
    margin-bottom: 122px;
}

.nav-item:nth-child(5) {
    margin-bottom: 261px;
}

.nav-item.active {
    background-color: #2AD95B;
    border-radius: 8px;
    padding: 9px 16px;
}

.nav-item i {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #1E1E1E;
    margin-right: 17px;
}

.nav-item span {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 25px;
    line-height: 1.21;
    color: #FFFFFF;
}

.nav-item:hover {
    background-color: rgba(42, 217, 91, 0.1);
    border-radius: 8px;
    padding: 9px 16px;
}

/* Main Content */
.main-content {
    margin-left: 254px;
    width: calc(100vw - 254px);
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Top Navbar */
.top-navbar {
    background-color: #FFFFFF;
    border-bottom: 1px solid #FFFFFF;
    height: 134px;
    display: flex;
    align-items: center;
    padding: 0 40px;
    box-shadow: 0px 4px 4px -4px rgba(12, 12, 13, 0.05);
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.page-title {
    font-family: 'Inter', sans-serif;
    font-weight: 800;
    font-size: 40px;
    line-height: 1.21;
    color: #000000;
    text-align: center;
}

.navbar-actions {
    display: flex;
    gap: 96px;
    align-items: center;
}

.icon-btn {
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.icon-btn i {
    font-size: 24px;
    color: #1E1E1E;
}

.icon-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: 40px;
    overflow-y: auto;
}

/* Statistics Section */
.stats-section {
    display: flex;
    gap: 134px;
    margin-bottom: 40px;
    justify-content: flex-start;
    padding-left: 84px;
}

.stat-card {
    background-color: #FFFFFF;
    border: 1px solid #D9D9D9;
    border-radius: 8px;
    padding: 32px;
    width: 466px;
    position: relative;
    box-shadow: 0px 4px 4px -4px rgba(12, 12, 13, 0.05), 0px 16px 32px -4px rgba(12, 12, 13, 0.1);
}

.close-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 32px;
    height: 32px;
    background: none;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn i {
    font-size: 16px;
    color: #1E1E1E;
}

.close-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.card-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.card-title {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 45px;
    line-height: 1.16;
    color: #1E1E1E;
    margin: 0;
}

.card-value {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 24px;
    line-height: 1.2;
    letter-spacing: -2%;
    color: #1E1E1E;
    margin: 0 0 24px 0;
}

.detail-btn {
    background-color: #2C2C2C;
    border: 1px solid #2C2C2C;
    border-radius: 8px;
    padding: 12px;
    color: #F5F5F5;
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 24px;
    line-height: 1.2;
    letter-spacing: -2%;
    cursor: pointer;
    align-self: flex-end;
    transition: all 0.3s ease;
}

.detail-btn:hover {
    background-color: #1a1a1a;
    border-color: #1a1a1a;
}

/* Parking Section */
.parking-section {
    padding-left: 21px;
}

.parking-header {
    margin-bottom: 32px;
}

.parking-title {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 24px;
    line-height: 1.2;
    letter-spacing: -2%;
    color: #000000;
}

.parking-grid {
    display: grid;
    grid-template-columns: repeat(4, 275px);
    grid-template-rows: repeat(4, 55px);
    gap: 19px 16px;
    max-width: 1144px;
}

.parking-slot {
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.parking-slot.occupied {
    background-color: #FF0B1F;
}

.parking-slot.available {
    background-color: #0BFF1F;
}

.slot-number {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 24px;
    line-height: 1.2;
    letter-spacing: -2%;
    color: #FFFFFF;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.parking-slot:hover {
    transform: translateY(-2px);
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stats-section {
        flex-direction: column;
        gap: 20px;
        align-items: center;
        padding-left: 0;
    }
    
    .parking-grid {
        grid-template-columns: repeat(2, 275px);
        grid-template-rows: repeat(8, 55px);
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 80px;
    }
    
    .sidebar .nav-item span {
        display: none;
    }
    
    .main-content {
        margin-left: 80px;
        width: calc(100vw - 80px);
    }
    
    .parking-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(16, 55px);
    }
    
    .stat-card {
        width: 100%;
        max-width: 400px;
    }
}
