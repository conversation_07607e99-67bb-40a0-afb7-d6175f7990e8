import { useMemo, useState, useEffect } from "react";
import { useResponsive } from "@/hooks/useResponsive";
import { REVENUE_TABLE_COLUMN_WIDTHS } from "@/constants/constants";

// Extended responsive state with width for calculations
interface ExtendedResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  width: number;
}

// Custom hook that extends useResponsive with width
function useResponsiveState(): ExtendedResponsiveState {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  const [width, setWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);
  
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleResize = () => setWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return {
    isMobile,
    isTablet,
    isDesktop,
    width,
  };
}

// Interface for revenue table responsive configuration
export interface RevenueTableResponsiveConfig {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  shouldUseCardView: boolean;
  tableScrollX: number | string;
  maxVisiblePeriods: number;
  columnWidth: {
    metric: number;
    period: number;
    total: number;
  };
}

/**
 * Custom hook to manage responsive behavior for revenue table
 * Handles column visibility, scroll configuration, and mobile card view
 */
export function useRevenueTableResponsive(periodsCount: number): RevenueTableResponsiveConfig {
  const { isMobile, isTablet, isDesktop, width } = useResponsiveState();
  
  const config = useMemo((): RevenueTableResponsiveConfig => {
    // Determine if we should show card view instead of table
    const shouldUseCardView = isMobile;
    
    // Calculate max visible periods based on screen width
    let maxVisiblePeriods = periodsCount;
    
    // Define column widths based on screen size
    let metricWidth: number;
    let periodWidth: number;
    let totalWidth: number;
    
    if (isMobile) {
      // Mobile: Use card view, no column restrictions needed for table
      maxVisiblePeriods = periodsCount;
      metricWidth = REVENUE_TABLE_COLUMN_WIDTHS.METRIC_COLUMN;
      periodWidth = REVENUE_TABLE_COLUMN_WIDTHS.PERIOD_COLUMN;
      totalWidth = REVENUE_TABLE_COLUMN_WIDTHS.TOTAL_COLUMN;
    } else if (isTablet) {
      // Tablet: Adjust column widths and limit visible periods
      metricWidth = 150;
      periodWidth = 120;
      totalWidth = 150;
      
      // Calculate how many periods can fit on tablet
      const availableWidth = width - metricWidth - totalWidth - 100; // 100px for padding/margins
      maxVisiblePeriods = Math.max(2, Math.floor(availableWidth / periodWidth));
    } else {
      // Desktop: Use default column widths
      metricWidth = REVENUE_TABLE_COLUMN_WIDTHS.METRIC_COLUMN;
      periodWidth = REVENUE_TABLE_COLUMN_WIDTHS.PERIOD_COLUMN;
      totalWidth = REVENUE_TABLE_COLUMN_WIDTHS.TOTAL_COLUMN;
      
      const availableWidth = width - metricWidth - totalWidth - 200; // More padding for desktop
      maxVisiblePeriods = Math.max(3, Math.floor(availableWidth / periodWidth));
    }
    
    // Ensure we don't exceed actual periods count
    maxVisiblePeriods = Math.min(maxVisiblePeriods, periodsCount);
    
    // Calculate scroll width for table
    const tableScrollX = shouldUseCardView 
      ? "max-content"
      : metricWidth + (maxVisiblePeriods * periodWidth) + totalWidth;
    
    return {
      isMobile,
      isTablet,
      isDesktop,
      shouldUseCardView,
      tableScrollX,
      maxVisiblePeriods,
      columnWidth: {
        metric: metricWidth,
        period: periodWidth,
        total: totalWidth,
      },
    };
  }, [isMobile, isTablet, isDesktop, width, periodsCount]);
  
  return config;
}

/**
 * Helper function to get mobile card configuration for revenue data
 */
export function getRevenueCardConfig() {
  return {
    mobileCardTitle: (record: any) => record.metric,
    mobileGridCols: 1 as const,
  };
}
