# Stock Aggregation Feature - T<PERSON><PERSON> hợp Stock từ Nhiều Mã Kho

## Tổng quan

Chức năng mới được thêm vào hệ thống scraping Arito để tổng hợp stock (tồn kho) từ nhiều mã kho khác nhau cho cùng một mã vật tư.

### Vấn đề trước đây
- Một mã vật tư có thể tồn tại ở nhiều mã kho khác nhau (A01, A02, A03...)
- Hệ thống chỉ cập nhật stock của row gần nhất được scraping
- Không tổng hợp stock từ tất cả các kho

### Giải pháp mới
- Tổng hợp stock từ tất cả mã kho có cùng mã vật tư
- Stock cuối cùng = Tổng stock từ tất cả kho
- Vẫn giữ validation: stock >= 0 và là số nguyên dương

## Cấu trúc <PERSON>ữ liệu

### Input (từ scraping)
```json
[
  {"ma_kho": "A01", "ma_vt": "VT001", "ton_cuoi": 50},
  {"ma_kho": "A02", "ma_vt": "VT001", "ton_cuoi": 100},
  {"ma_kho": "A03", "ma_vt": "VT001", "ton_cuoi": 25},
  {"ma_kho": "A01", "ma_vt": "VT002", "ton_cuoi": 75}
]
```

### Output (sau aggregation)
```json
{
  "VT001": {
    "total_stock": 175,
    "warehouses": ["A01", "A02", "A03"],
    "warehouse_stocks": {"A01": 50, "A02": 100, "A03": 25},
    "valid_items": 3
  },
  "VT002": {
    "total_stock": 75,
    "warehouses": ["A01"],
    "warehouse_stocks": {"A01": 75},
    "valid_items": 1
  }
}
```

## Các Hàm Mới

### 1. `aggregate_stock_by_product_code(extracted_data)`

**Mục đích**: Tổng hợp stock theo mã vật tư từ nhiều mã kho

**Input**: 
- `extracted_data`: List[Dict] - Dữ liệu từ scraping

**Output**: 
- Dict[ma_vt] - Thông tin tổng hợp cho mỗi mã vật tư

**Tính năng**:
- ✅ Tổng hợp stock từ nhiều kho
- ✅ Validation stock âm (chuyển thành 0)
- ✅ Xử lý các kiểu dữ liệu khác nhau (int, float, Decimal, string)
- ✅ Bỏ qua dữ liệu không hợp lệ
- ✅ Logging chi tiết quá trình aggregation

### 2. `update_product_inventory()` - Đã cập nhật

**Thay đổi**:
- Sử dụng `aggregate_stock_by_product_code()` trước khi cập nhật database
- Cập nhật với stock tổng hợp thay vì stock từng item
- Logging cải thiện với thông tin về warehouses

## Validation và Edge Cases

### 1. Stock âm
```python
# Input: ton_cuoi = -10
# Output: stock_value = 0
# Log: "Negative stock for VT001 in warehouse A01, setting to 0"
```

### 2. Dữ liệu không hợp lệ
```python
# ma_vt trống -> bỏ qua
# ton_cuoi = None -> bỏ qua  
# ton_cuoi = "invalid" -> bỏ qua với log warning
```

### 3. Kiểu dữ liệu khác nhau
```python
# int: 50 -> 50.0
# float: 50.5 -> 50.5
# Decimal: Decimal('50.5') -> 50.5
# string: "50,5" -> 50.5 (replace comma with dot)
```

### 4. Chuyển đổi cuối cùng
```python
# total_stock luôn được chuyển thành int
# 50.7 -> 50 (truncate, không round)
```

## Logging Cải thiện

### Aggregation Logs
```
Product VT001: aggregated stock = 175 from 3 warehouse(s): A01, A02, A03
Stock aggregation completed: 5 unique products aggregated
```

### Update Logs
```
Updated product VT001: stock 0 -> 175 (aggregated from 3 warehouse(s): A01, A02, A03)
Inventory update completed: 3 products updated, 1 not found in database, 5 unique products aggregated from 12 raw items
```

## Testing

### 1. Unit Test
```bash
cd full/backend
python test_aggregation_simple.py
```

### 2. Integration Test (cần Django)
```bash
cd full/backend
source myenv/Scripts/activate  # hoặc venv/Scripts/activate
python test_scraping_with_aggregation.py
```

### 3. Test Cases
- ✅ Tổng hợp từ nhiều kho
- ✅ Sản phẩm chỉ có 1 kho
- ✅ Stock âm được chuyển thành 0
- ✅ Dữ liệu không hợp lệ bị bỏ qua
- ✅ Sản phẩm không tồn tại trong database
- ✅ Kiểu dữ liệu Decimal

## Backward Compatibility

- ✅ Interface của `update_product_inventory()` không thay đổi
- ✅ Return format giữ nguyên, chỉ thêm `aggregation_summary`
- ✅ Existing scraping flow hoạt động bình thường
- ✅ Không ảnh hưởng đến các chức năng khác

## Performance

### Cải thiện
- Giảm số lần truy cập database (từ N items thành M unique products)
- Aggregation trong memory trước khi update database

### Complexity
- Time: O(N) cho aggregation + O(M) cho database updates
- Space: O(M) cho aggregated data
- Với N = số items từ scraping, M = số unique products

## Ví dụ Thực tế

### Trước khi cập nhật
```
Scraping data:
- A01 | VT001 | 50  -> Cập nhật VT001 = 50
- A02 | VT001 | 100 -> Cập nhật VT001 = 100 (ghi đè)
- A03 | VT001 | 25  -> Cập nhật VT001 = 25 (ghi đè)

Kết quả: VT001 stock = 25 (chỉ từ kho cuối cùng)
```

### Sau khi cập nhật
```
Scraping data:
- A01 | VT001 | 50
- A02 | VT001 | 100  
- A03 | VT001 | 25

Aggregation: VT001 total = 50 + 100 + 25 = 175
Kết quả: VT001 stock = 175 (tổng từ tất cả kho)
```

## Files Modified

1. **`store/services/scraping_service.py`**
   - Thêm hàm `aggregate_stock_by_product_code()`
   - Cập nhật hàm `update_product_inventory()`
   - Cải thiện logging

2. **Test Files** (mới)
   - `test_aggregation_simple.py` - Unit test
   - `test_scraping_with_aggregation.py` - Integration test

3. **Documentation** (mới)
   - `README_STOCK_AGGREGATION.md` - Tài liệu này
