import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, Pagin<PERSON> } from "antd";
import { useAuth } from "@/context/auth-hooks";
import { OrderSearch } from "@/components/orders/OrderSearch";
import { ChainOrderStatusTabs } from "@/components/orders/ChainOrderStatusTabs";
import { OrderTable } from "@/components/orders/OrderTable";
import { OrderFilterControl } from "@/components/orders/OrderFilterControl";
import { StaffFilter } from "@/components/orders/StaffFilter";
import { useTableOrders } from "@/hooks/useTableOrders";
import { ITEMS_PER_PAGE } from "@/constants/constants";

export default function ChainOrdersPage() {
  const navigate = useNavigate();
  const { user } = useAuth();

  const {
    page,
    statusFilter,
    searchParams,
    selectedSalesAdmin,
    selectedSalesAdmins,
    selectedDeliveryStaff,
    selectedDeliveryStaffs,
    selectedShippingUnit,
    selectedShippingUnits,
    paymentStatusFilter,
    orders,
    totalCount,
    statusCounts,
    staffList,
    isLoading,
    setPage,
    handleSearch,
    handleStatusChange,
    handleSalesAdminChange,
    handleSalesAdminsChange,
    handleDeliveryStaffChange,
    handleDeliveryStaffsChange,
    handleShippingUnitChange,
    handleShippingUnitsChange,
    handlePaymentStatusFilterChange,
    updateOrderStatus,
    updateDeliveryMethod,
    updatePaymentMethod,
    updatePaymentStatus,
    updatePrintedStatus,
  } = useTableOrders({
    userId: user?.id,
    userRole: user?.role,
    chainOnly: true, // Thêm flag để chỉ lấy đơn chuỗi
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Đơn chuỗi</h1>
        {(user?.role === "sales_admin" ||
          user?.role === "sales_manager" ||
          user?.role === "accounting_staff") && (
          <div className="flex gap-4 items-center">
            <OrderFilterControl
              currentPaymentStatus={paymentStatusFilter}
              onPaymentStatusChange={handlePaymentStatusFilterChange}
            />
            <Button
              type="primary"
              onClick={() => navigate("/orders/create-chain")}
              size="large"
            >
              Tạo Đơn Hàng Chuỗi
            </Button>
          </div>
        )}
      </div>

      <OrderSearch
        onSearch={handleSearch}
        initialParams={{ ...searchParams, enableMultiSelect: true }}
      />

      <StaffFilter
        userRole={user?.role || ""}
        salesAdmin={selectedSalesAdmin}
        selectedSalesAdmins={selectedSalesAdmins}
        deliveryStaff={selectedDeliveryStaff}
        selectedDeliveryStaffs={selectedDeliveryStaffs}
        shippingUnit={selectedShippingUnit}
        selectedShippingUnits={selectedShippingUnits}
        onSalesAdminChange={handleSalesAdminChange}
        onSalesAdminsChange={handleSalesAdminsChange}
        onDeliveryStaffChange={handleDeliveryStaffChange}
        onDeliveryStaffsChange={handleDeliveryStaffsChange}
        onShippingUnitChange={handleShippingUnitChange}
        onShippingUnitsChange={handleShippingUnitsChange}
        staffList={staffList}
        enableMultiSelect={true}
      />

      <ChainOrderStatusTabs
        currentStatus={statusFilter}
        onChange={handleStatusChange}
        counts={statusCounts}
      />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          <OrderTable
            orders={orders}
            staffList={staffList}
            onUpdateStatus={updateOrderStatus}
            onUpdateDeliveryMethod={updateDeliveryMethod}
            onUpdatePaymentMethod={updatePaymentMethod}
            onUpdatePaymentStatus={updatePaymentStatus}
            onUpdatePrintedStatus={updatePrintedStatus}
            isChainTable={true}
          />

          {orders.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              Không tìm thấy đơn hàng nào phù hợp.
            </div>
          )}

          {orders.length > 0 && (
            <div className="flex justify-center mt-4">
              <Pagination
                current={page}
                total={totalCount}
                pageSize={ITEMS_PER_PAGE}
                onChange={(page) => setPage(page)}
                showSizeChanger={false}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
